menu "Camera configuration"

    config OV7670_SUPPORT
        bool "Support OV7670 VGA"
        default y
        help
            Enable this option if you want to use the OV7670.
            Disable this option to save memory.

    config OV7725_SUPPORT
        bool "Support OV7725 VGA"
        default y
        help
            Enable this option if you want to use the OV7725.
            Disable this option to save memory.

    config NT99141_SUPPORT
        bool "Support NT99141 HD"
        default y
        help
            Enable this option if you want to use the NT99141.
            Disable this option to save memory.

    config OV2640_SUPPORT
        bool "Support OV2640 2MP"
        default y
        help
            Enable this option if you want to use the OV2640.
            Disable this option to save memory.

    config OV3660_SUPPORT
        bool "Support OV3660 3MP"
        default y
        help
            Enable this option if you want to use the OV3360.
            Disable this option to save memory.

    config OV5640_SUPPORT
        bool "Support OV5640 5MP"
        default y
        help
            Enable this option if you want to use the OV5640.
            Disable this option to save memory.

    config GC2145_SUPPORT
        bool "Support GC2145 2MP"
        default y
        help
            Enable this option if you want to use the GC2145.
            Disable this option to save memory.

    config GC032A_SUPPORT
        bool "Support GC032A VGA"
        default y
        help
            Enable this option if you want to use the GC032A.
            Disable this option to save memory.

    config GC0308_SUPPORT
        bool "Support GC0308 VGA"
        default y
        help
            Enable this option if you want to use the GC0308.
            Disable this option to save memory.
            
    config BF3005_SUPPORT
        bool "Support BF3005(BYD3005) VGA"
        default y
        help
            Enable this option if you want to use the BF3005.
            Disable this option to save memory.
            
    config BF20A6_SUPPORT
        bool "Support BF20A6(BYD20A6) VGA"
        default y
        help
            Enable this option if you want to use the BF20A6.
            Disable this option to save memory.

    config SC101IOT_SUPPORT
        bool "Support SC101IOT HD"
        default n
        help
            Enable this option if you want to use the SC101IOT.
            Disable this option to save memory.

    choice SC101_REGS_SELECT
        prompt "SC101iot default regs"
        default SC101IOT_720P_15FPS_ENABLED
        depends on SC101IOT_SUPPORT
        help
            Currently SC010iot has several register sets available.
            Select the one that matches your needs.

        config SC101IOT_720P_15FPS_ENABLED
            bool "xclk20M_720p_15fps"
        help
            Select this option means that when xclk is 20M, the frame rate is 15fps at 720p resolution.
        config SC101IOT_VGA_25FPS_ENABLED
            bool "xclk20M_VGA_25fps"
        help
            Select this option means that when xclk is 20M, the frame rate is 25fps at VGA resolution.
    endchoice

    config SC030IOT_SUPPORT
        bool "Support SC030IOT VGA"
        default y
        help
            Enable this option if you want to use the SC030IOT.
            Disable this option to save memory.
    
    config SC031GS_SUPPORT
        bool "Support SC031GS VGA"
        default n
        help
            SC031GS is a global shutter CMOS sensor with high frame rate and single-frame HDR.
            Enable this option if you want to use the SC031GS.
            Disable this option to save memory.

    choice SCCB_HARDWARE_I2C_PORT
        bool "I2C peripheral to use for SCCB"
        default SCCB_HARDWARE_I2C_PORT1

        config SCCB_HARDWARE_I2C_PORT0
            bool "I2C0"
        config SCCB_HARDWARE_I2C_PORT1
            bool "I2C1"

    endchoice

    config SCCB_CLK_FREQ
    int "SCCB clk frequency"
    default 100000
    range 100000 400000
    help
        Increasing this value can reduce the initialization time of the sensor.
        Please refer to the relevant instructions of the sensor to adjust the value.
    
    choice GC_SENSOR_WINDOW_MODE
        bool "GalaxyCore Sensor Window Mode"
        depends on (GC2145_SUPPORT || GC032A_SUPPORT || GC0308_SUPPORT)
        default GC_SENSOR_SUBSAMPLE_MODE
        help
            This option determines how to reduce the output size when the resolution you set is less than the maximum resolution.
            SUBSAMPLE_MODE has a bigger perspective and WINDOWING_MODE has a higher frame rate.

        config GC_SENSOR_WINDOWING_MODE
            bool "Windowing Mode"
        config GC_SENSOR_SUBSAMPLE_MODE
            bool "Subsample Mode"
    endchoice

    config CAMERA_TASK_STACK_SIZE
        int "CAM task stack size"
        default 2048
        help
            Camera task stack size

    choice CAMERA_TASK_PINNED_TO_CORE
        bool "Camera task pinned to core"
        default CAMERA_CORE0
        help
            Pin the camera handle task to a certain core(0/1). It can also be done automatically choosing NO_AFFINITY.

        config CAMERA_CORE0
            bool "CORE0"
        config CAMERA_CORE1
            bool "CORE1"
        config CAMERA_NO_AFFINITY
            bool "NO_AFFINITY"

    endchoice

    config CAMERA_DMA_BUFFER_SIZE_MAX
        int "DMA buffer size"
        range 8192 32768
        default 32768
        help
            Maximum value of DMA buffer
            Larger values may fail to allocate due to insufficient contiguous memory blocks, and smaller value may cause DMA interrupt to be too frequent.

    config CAMERA_CONVERTER_ENABLED
        bool "Enable camera RGB/YUV converter"
        depends on IDF_TARGET_ESP32S3
        default n
        help
            Enable this option if you want to use RGB565/YUV422/YUV420/YUV411 format conversion.

    choice CAMERA_CONV_PROTOCOL
        bool "Camera converter protocol"
        depends on CAMERA_CONVERTER_ENABLED
        default LCD_CAM_CONV_BT601_ENABLED
        help
            Supports format conversion under both BT601 and BT709 standards.

        config LCD_CAM_CONV_BT601_ENABLED
            bool "BT601"
        config LCD_CAM_CONV_BT709_ENABLED
            bool "BT709"
    endchoice

    config LCD_CAM_CONV_FULL_RANGE_ENABLED
        bool "Camera converter full range mode"
        depends on CAMERA_CONVERTER_ENABLED
        default y
        help
            Supports format conversion under both full color range mode and limited color range mode.
            If full color range mode is selected, the color range of RGB or YUV is 0~255.
            If limited color range mode is selected, the color range of RGB is 16~240, and the color range of YUV is Y[16~240], UV[16~235].
            Full color range mode has a wider color range, so details in the image show more clearly.
            Please confirm the color range mode of the current camera sensor, incorrect color range mode may cause color difference in the final converted image.
            Full range mode is used by default. If this option is not selected, the format conversion function will be done using the limited range mode.
endmenu
