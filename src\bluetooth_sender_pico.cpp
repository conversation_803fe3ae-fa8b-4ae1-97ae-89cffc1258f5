/*
 * ESP32-S3 PICO 蓝牙发送端
 * 用于向ESP32-S3 DevKitC发送数据
 */

#include <Arduino.h>
#include "BLEDevice.h"
#include "BLEServer.h"
#include "BLEUtils.h"
#include "BLE2902.h"
#include <ArduinoJson.h>

// 蓝牙服务和特征UUID
#define SERVICE_UUID        "12345678-1234-1234-1234-123456789abc"
#define CHARACTERISTIC_UUID "*************-4321-4321-cba987654321"

// 蓝牙相关变量
BLEServer* pServer = NULL;
BLECharacteristic* pCharacteristic = NULL;
bool deviceConnected = false;
bool oldDeviceConnected = false;

// 数据发送相关
unsigned long lastDataSendTime = 0;
const unsigned long DATA_SEND_INTERVAL = 1000; // 每秒发送一次数据

// 模拟传感器数据
struct SensorData {
    float temperature;
    float humidity;
    float light_level;
    int motion_detected;
    String status;
};

// 蓝牙服务器回调
class MyServerCallbacks: public BLEServerCallbacks {
    void onConnect(BLEServer* pServer) {
        deviceConnected = true;
        Serial.println("设备已连接");
    };

    void onDisconnect(BLEServer* pServer) {
        deviceConnected = false;
        Serial.println("设备已断开连接");
    }
};

// 初始化蓝牙
void initBluetooth() {
    Serial.println("初始化蓝牙...");
    
    // 创建BLE设备
    BLEDevice::init("ESP32-S3-PICO-Sender");
    
    // 创建BLE服务器
    pServer = BLEDevice::createServer();
    pServer->setCallbacks(new MyServerCallbacks());

    // 创建BLE服务
    BLEService *pService = pServer->createService(SERVICE_UUID);

    // 创建BLE特征
    pCharacteristic = pService->createCharacteristic(
                         CHARACTERISTIC_UUID,
                         BLECharacteristic::PROPERTY_READ |
                         BLECharacteristic::PROPERTY_WRITE |
                         BLECharacteristic::PROPERTY_NOTIFY
                       );

    // 添加描述符
    pCharacteristic->addDescriptor(new BLE2902());

    // 启动服务
    pService->start();

    // 启动广播
    BLEAdvertising *pAdvertising = BLEDevice::getAdvertising();
    pAdvertising->addServiceUUID(SERVICE_UUID);
    pAdvertising->setScanResponse(false);
    pAdvertising->setMinPreferred(0x0);  // set value to 0x00 to not advertise this parameter
    BLEDevice::startAdvertising();
    
    Serial.println("蓝牙服务已启动，等待连接...");
    Serial.println("设备名称: ESP32-S3-PICO-Sender");
}

// 生成模拟传感器数据
SensorData generateSensorData() {
    SensorData data;
    
    // 模拟温度数据 (20-30°C)
    data.temperature = 20.0 + (random(0, 1000) / 100.0);
    
    // 模拟湿度数据 (40-80%)
    data.humidity = 40.0 + (random(0, 4000) / 100.0);
    
    // 模拟光照强度 (0-1000 lux)
    data.light_level = random(0, 1000);
    
    // 模拟运动检测 (0或1)
    data.motion_detected = random(0, 2);
    
    // 状态信息
    if (data.motion_detected) {
        data.status = "Motion Detected";
    } else {
        data.status = "No Motion";
    }
    
    return data;
}

// 发送数据到接收端
void sendDataToBLE() {
    if (!deviceConnected) {
        return;
    }
    
    // 检查发送间隔
    unsigned long currentTime = millis();
    if (currentTime - lastDataSendTime < DATA_SEND_INTERVAL) {
        return;
    }
    lastDataSendTime = currentTime;
    
    // 生成传感器数据
    SensorData sensorData = generateSensorData();
    
    // 创建JSON数据
    DynamicJsonDocument doc(512);
    doc["device"] = "ESP32-S3-PICO";
    doc["timestamp"] = currentTime;
    doc["temperature"] = sensorData.temperature;
    doc["humidity"] = sensorData.humidity;
    doc["light_level"] = sensorData.light_level;
    doc["motion_detected"] = sensorData.motion_detected;
    doc["status"] = sensorData.status;
    doc["battery_level"] = random(20, 100); // 模拟电池电量
    
    String jsonString;
    serializeJson(doc, jsonString);
    
    // 通过BLE发送数据
    pCharacteristic->setValue(jsonString.c_str());
    pCharacteristic->notify();
    
    Serial.println("数据已发送: " + jsonString);
}

// 处理蓝牙连接状态
void handleBluetoothConnection() {
    // 断开连接后重新开始广播
    if (!deviceConnected && oldDeviceConnected) {
        delay(500); // 给蓝牙栈一些时间准备
        pServer->startAdvertising(); // 重新开始广播
        Serial.println("重新开始广播");
        oldDeviceConnected = deviceConnected;
    }
    
    // 连接建立
    if (deviceConnected && !oldDeviceConnected) {
        oldDeviceConnected = deviceConnected;
    }
}

void setup() {
    // 初始化串口
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("=================================");
    Serial.println("ESP32-S3 PICO 蓝牙发送端启动");
    Serial.println("=================================");
    
    // 初始化随机数种子
    randomSeed(analogRead(0));
    
    // 初始化蓝牙
    initBluetooth();
    
    Serial.println("系统初始化完成");
}

void loop() {
    // 处理蓝牙连接状态
    handleBluetoothConnection();
    
    // 发送数据
    sendDataToBLE();
    
    // 输出连接状态
    static unsigned long lastStatusTime = 0;
    if (millis() - lastStatusTime > 5000) {
        Serial.printf("连接状态: %s\n", deviceConnected ? "已连接" : "未连接");
        Serial.printf("自由堆内存: %d bytes\n", ESP.getFreeHeap());
        lastStatusTime = millis();
    }
    
    delay(100);
}
