/*
 * 蓝牙集成示例 - 如何在现有项目中添加蓝牙功能
 * 这个示例展示了如何将蓝牙功能集成到您现有的摄像头项目中
 */

#include <Arduino.h>
#include "BLEDevice.h"
#include "BLEServer.h"
#include "BLEUtils.h"
#include "BLE2902.h"
#include <ArduinoJson.h>

// 蓝牙配置
#define BT_SERVICE_UUID        "12345678-1234-1234-1234-123456789abc"
#define BT_CHARACTERISTIC_UUID "*************-4321-4321-cba987654321"

// 蓝牙变量
BLEServer* btServer = NULL;
BLECharacteristic* btCharacteristic = NULL;
bool btConnected = false;

// 蓝牙服务器回调
class BluetoothServerCallbacks: public BLEServerCallbacks {
    void onConnect(BLEServer* pServer) {
        btConnected = true;
        Serial.println("[BT] 设备已连接");
    }

    void onDisconnect(BLEServer* pServer) {
        btConnected = false;
        Serial.println("[BT] 设备已断开连接");
        // 重新开始广播
        delay(500);
        pServer->startAdvertising();
    }
};

// 初始化蓝牙功能
bool initBluetooth() {
    Serial.println("[BT] 初始化蓝牙...");
    
    try {
        // 创建BLE设备
        BLEDevice::init("ESP32-S3-Camera-Device");
        
        // 创建BLE服务器
        btServer = BLEDevice::createServer();
        btServer->setCallbacks(new BluetoothServerCallbacks());

        // 创建BLE服务
        BLEService *btService = btServer->createService(BT_SERVICE_UUID);

        // 创建BLE特征
        btCharacteristic = btService->createCharacteristic(
                             BT_CHARACTERISTIC_UUID,
                             BLECharacteristic::PROPERTY_READ |
                             BLECharacteristic::PROPERTY_WRITE |
                             BLECharacteristic::PROPERTY_NOTIFY
                           );

        // 添加描述符
        btCharacteristic->addDescriptor(new BLE2902());

        // 启动服务
        btService->start();

        // 启动广播
        BLEAdvertising *btAdvertising = BLEDevice::getAdvertising();
        btAdvertising->addServiceUUID(BT_SERVICE_UUID);
        btAdvertising->setScanResponse(false);
        btAdvertising->setMinPreferred(0x0);
        BLEDevice::startAdvertising();
        
        Serial.println("[BT] 蓝牙服务已启动");
        return true;
    } catch (const std::exception& e) {
        Serial.println("[BT] 蓝牙初始化失败");
        return false;
    }
}

// 通过蓝牙发送摄像头检测结果
void sendCameraDataViaBluetooth(const String& postureStatus, const String& emotionStatus) {
    if (!btConnected || !btCharacteristic) {
        return;
    }
    
    // 创建包含摄像头检测结果的JSON
    DynamicJsonDocument doc(512);
    doc["device"] = "ESP32-S3-Camera";
    doc["timestamp"] = millis();
    doc["posture"] = postureStatus;
    doc["emotion"] = emotionStatus;
    doc["camera_active"] = true;
    doc["free_heap"] = ESP.getFreeHeap();
    
    String jsonString;
    serializeJson(doc, jsonString);
    
    // 发送数据
    btCharacteristic->setValue(jsonString.c_str());
    btCharacteristic->notify();
    
    Serial.println("[BT] 数据已发送: " + jsonString);
}

// 示例：集成到现有的摄像头检测循环中
void cameraDetectionWithBluetooth() {
    // 这里是您现有的摄像头检测代码的简化版本
    
    // 模拟获取检测结果
    String currentPosture = "Good Posture";  // 从您的检测算法获取
    String currentEmotion = "Happy";         // 从您的检测算法获取
    
    // 通过蓝牙发送结果
    static unsigned long lastBtSend = 0;
    if (millis() - lastBtSend > 2000) {  // 每2秒发送一次
        sendCameraDataViaBluetooth(currentPosture, currentEmotion);
        lastBtSend = millis();
    }
}

// 在您的现有setup()函数中添加这些代码
void setupWithBluetooth() {
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("=================================");
    Serial.println("摄像头检测系统 + 蓝牙功能");
    Serial.println("=================================");
    
    // 您现有的摄像头初始化代码...
    // initCamera();
    // initWiFi();
    // etc...
    
    // 添加蓝牙初始化
    if (initBluetooth()) {
        Serial.println("蓝牙功能已启用");
    } else {
        Serial.println("蓝牙功能启动失败，继续运行摄像头功能");
    }
    
    Serial.println("系统初始化完成");
}

// 在您的现有loop()函数中添加这些代码
void loopWithBluetooth() {
    // 您现有的主循环代码...
    // handleWebServer();
    // processCameraFrames();
    // etc...
    
    // 添加蓝牙数据发送
    cameraDetectionWithBluetooth();
    
    delay(100);
}

// ============================================
// 如何集成到您现有的main.cpp中：
// ============================================

/*
1. 在您的main.cpp文件顶部添加蓝牙相关的include：
   #include "BLEDevice.h"
   #include "BLEServer.h"
   #include "BLEUtils.h"
   #include "BLE2902.h"

2. 复制上面的蓝牙相关函数到您的main.cpp中

3. 在setup()函数中添加蓝牙初始化：
   if (initBluetooth()) {
       Serial.println("蓝牙功能已启用");
   }

4. 在您的检测结果报告函数中添加蓝牙发送：
   void report_posture_status() {
       calculate_posture_stats();
       String posture_status = getPostureStateName(current_posture_stats.dominant_state);
       Serial.println(posture_status);
       
       // 添加这行来通过蓝牙发送
       static String last_emotion = "";
       sendCameraDataViaBluetooth(posture_status, last_emotion);
   }

5. 在report_emotion_status()函数中也添加类似的代码

6. 确保在platformio.ini中添加了蓝牙库依赖：
   lib_deps =
       esp32-camera@^2.0.4
       bblanchon/ArduinoJson@^7.0.4
       ESP32 BLE Arduino@^2.0.0
*/

void setup() {
    setupWithBluetooth();
}

void loop() {
    loopWithBluetooth();
}
