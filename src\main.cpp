/*
 * ATOMS3R 实时摄像头检测系统
 * 支持连续帧获取和视觉处理
 */

#include <Arduino.h>
#include "esp_camera.h"
#include "camera_pins.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include <WiFi.h>
#include <WebServer.h>
#include <WiFiClient.h>
#include <ArduinoJson.h>

// WiFi配置 - 请修改为您的实际WiFi网络
const char* ssid = "ESP32Test";
const char* password = "12345678";

// WiFi数据发送配置
#define ENABLE_WIFI_DATA_SEND true   // 启用WiFi数据发送
const char* wifi_target_ip = "**************";   // ESP32S3接收端的实际IP地址
const int wifi_target_port = 8080;               // 目标端口

// Web服务器
WebServer server(80);

// WiFi客户端用于数据发送
WiFiClient wifiClient;
unsigned long lastDataSendTime = 0;
const unsigned long DATA_SEND_INTERVAL = 1000; // 每秒发送一次数据

// 摄像头配置
camera_config_t config;

// 坐姿检测配置
#define TARGET_FPS 10                    // 目标帧率 10Hz (0.1s间隔)
#define FRAME_INTERVAL_MS (1000/TARGET_FPS)  // 帧间隔 100ms
#define MAX_PROCESSING_TIME_MS 80        // 最大处理时间
#define POSTURE_HISTORY_SIZE 10          // 1秒内的检测历史(10次检测)

// 性能统计
struct PerformanceStats {
    unsigned long total_frames;
    unsigned long successful_frames;
    unsigned long failed_frames;
    unsigned long last_fps_calc_time;
    float current_fps;
    unsigned long max_processing_time;
    unsigned long min_processing_time;
    unsigned long avg_processing_time;
} perf_stats = {0};

// 任务句柄
TaskHandle_t camera_task_handle = NULL;

// 坐姿状态枚举
enum PostureState {
    POSTURE_NO_PERSON = -1,  // 无人检测
    POSTURE_GOOD = 0,        // 坐姿端正
    POSTURE_MILD = 1,        // 轻度不端正
    POSTURE_MODERATE = 2,    // 中度不端正
    POSTURE_SEVERE = 3       // 重度不端正
};

// 表情状态枚举
enum EmotionState {
    EMOTION_NO_FACE = -1,    // 无人脸检测
    EMOTION_ANGRY = 0,       // 愤怒
    EMOTION_EXCITED = 1,     // 兴奋
    EMOTION_SAD = 2,         // 悲伤
    EMOTION_HAPPY = 3,       // 开心
    EMOTION_CALM = 4         // 平静
};

// 坐姿检测结果
struct PostureResult {
    PostureState state;
    float confidence;        // 置信度 0-1
    float head_tilt;         // 头部倾斜角度
    float shoulder_slope;    // 肩膀倾斜度
    float body_center_offset; // 身体中心偏移
    bool person_detected;    // 是否检测到人物
    float person_confidence; // 人物检测置信度
};

// 表情检测结果
struct EmotionResult {
    EmotionState state;
    float confidence;        // 置信度 0-1
    bool face_detected;      // 是否检测到人脸
    float face_confidence;   // 人脸检测置信度
    float eye_openness;      // 眼部开合度
    float mouth_curve;       // 嘴部弯曲度
    float eyebrow_position;  // 眉毛位置
    float face_symmetry;     // 面部对称性
};

// 坐姿历史记录
PostureState posture_history[POSTURE_HISTORY_SIZE];
int posture_history_index = 0;
unsigned long last_posture_report_time = 0;

// 表情历史记录
EmotionState emotion_history[POSTURE_HISTORY_SIZE];
int emotion_history_index = 0;
unsigned long last_emotion_report_time = 0;

// 坐姿统计
struct PostureStats {
    int no_person_count;
    int good_count;
    int mild_count;
    int moderate_count;
    int severe_count;
    PostureState dominant_state;
} current_posture_stats = {0};

// 表情统计
struct EmotionStats {
    int no_face_count;
    int angry_count;
    int excited_count;
    int sad_count;
    int happy_count;
    int calm_count;
    EmotionState dominant_state;
} current_emotion_stats = {0};

// Get posture state name in English
const char* getPostureStateName(PostureState state) {
    switch(state) {
        case POSTURE_NO_PERSON: return "No People";
        case POSTURE_GOOD: return "Good Posture";
        case POSTURE_MILD: return "Mild Poor Posture";
        case POSTURE_MODERATE: return "Moderate Poor Posture";
        case POSTURE_SEVERE: return "Severe Poor Posture";
        default: return "Unknown";
    }
}

// Get emotion state name in English
const char* getEmotionStateName(EmotionState state) {
    switch(state) {
        case EMOTION_NO_FACE: return "No Face";
        case EMOTION_ANGRY: return "Angry";
        case EMOTION_EXCITED: return "Excited";
        case EMOTION_SAD: return "Sad";
        case EMOTION_HAPPY: return "Happy";
        case EMOTION_CALM: return "Calm";
        default: return "Unknown";
    }
}

// Web服务器处理函数
void handleRoot() {
    String html = "<!DOCTYPE html><html><head><title>ATOMS3R Posture & Emotion Detection</title>";
    html += "<meta charset='UTF-8'>";
    html += "<style>body{font-family:Arial;text-align:center;margin:20px;}";
    html += "img{max-width:640px;height:auto;border:2px solid #333;}";
    html += ".status{font-size:20px;margin:15px;padding:10px;border-radius:5px;display:inline-block;min-width:200px;}";
    html += ".good{background-color:#d4edda;color:#155724;}";
    html += ".mild{background-color:#fff3cd;color:#856404;}";
    html += ".moderate{background-color:#f8d7da;color:#721c24;}";
    html += ".severe{background-color:#f5c6cb;color:#721c24;}";
    html += ".no-person{background-color:#e2e3e5;color:#383d41;}";
    html += ".angry{background-color:#ffcccb;color:#8b0000;}";
    html += ".excited{background-color:#ffeb9c;color:#b8860b;}";
    html += ".sad{background-color:#add8e6;color:#000080;}";
    html += ".happy{background-color:#98fb98;color:#006400;}";
    html += ".calm{background-color:#f0f8ff;color:#4682b4;}";
    html += ".no-face{background-color:#e2e3e5;color:#383d41;}";
    html += "</style></head><body>";
    html += "<h1>ATOMS3R 坐姿与表情检测系统</h1>";
    html += "<img src='/stream' alt='Camera Stream'>";
    html += "<div><div id='posture-status' class='status'>坐姿检测中...</div>";
    html += "<div id='emotion-status' class='status'>表情检测中...</div></div>";
    html += "<script>";
    html += "setInterval(function(){";
    html += "fetch('/posture-status').then(r=>r.text()).then(data=>{";
    html += "document.getElementById('posture-status').innerHTML='坐姿: '+data;";
    html += "var statusDiv=document.getElementById('posture-status');";
    html += "statusDiv.className='status';";
    html += "if(data.includes('Good')) statusDiv.className+=' good';";
    html += "else if(data.includes('Mild')) statusDiv.className+=' mild';";
    html += "else if(data.includes('Moderate')) statusDiv.className+=' moderate';";
    html += "else if(data.includes('Severe')) statusDiv.className+=' severe';";
    html += "else statusDiv.className+=' no-person';";
    html += "});";
    html += "fetch('/emotion-status').then(r=>r.text()).then(data=>{";
    html += "document.getElementById('emotion-status').innerHTML='表情: '+data;";
    html += "var emotionDiv=document.getElementById('emotion-status');";
    html += "emotionDiv.className='status';";
    html += "if(data.includes('Angry')) emotionDiv.className+=' angry';";
    html += "else if(data.includes('Excited')) emotionDiv.className+=' excited';";
    html += "else if(data.includes('Sad')) emotionDiv.className+=' sad';";
    html += "else if(data.includes('Happy')) emotionDiv.className+=' happy';";
    html += "else if(data.includes('Calm')) emotionDiv.className+=' calm';";
    html += "else emotionDiv.className+=' no-face';";
    html += "});}, 1000);";
    html += "</script></body></html>";
    server.send(200, "text/html", html);
}

void handleStream() {
    WiFiClient client = server.client();
    String response = "HTTP/1.1 200 OK\r\n";
    response += "Content-Type: multipart/x-mixed-replace; boundary=frame\r\n\r\n";
    client.print(response);

    int frame_count = 0;
    while (client.connected() && frame_count < 300) { // 限制最大帧数，避免长时间占用
        camera_fb_t* fb = esp_camera_fb_get();
        if (!fb) {
            delay(200);
            continue;
        }

        client.print("--frame\r\n");
        client.print("Content-Type: image/jpeg\r\n");
        client.print("Content-Length: " + String(fb->len) + "\r\n\r\n");
        client.write(fb->buf, fb->len);
        client.print("\r\n");

        esp_camera_fb_return(fb);
        frame_count++;
        delay(200); // 降低帧率，减少资源占用

        // 每10帧检查一次客户端连接状态
        if (frame_count % 10 == 0) {
            if (!client.connected()) break;
        }
    }
}

void handleStatus() {
    // 直接使用当前的主导状态，避免函数调用顺序问题
    String posture_status = getPostureStateName(current_posture_stats.dominant_state);
    server.send(200, "text/plain", posture_status);
}

void handlePostureStatus() {
    String posture_status = getPostureStateName(current_posture_stats.dominant_state);
    server.send(200, "text/plain", posture_status);
}

void handleEmotionStatus() {
    String emotion_status = getEmotionStateName(current_emotion_stats.dominant_state);
    server.send(200, "text/plain", emotion_status);
}

// 人物检测 - 基于图像特征分析
bool detect_person_presence(camera_fb_t* fb, float* confidence) {
    if (!fb || !fb->buf || fb->len == 0) {
        *confidence = 0.0;
        return false;
    }

    // 基于JPEG数据的人物检测
    if (fb->format == PIXFORMAT_JPEG) {
        // 分析JPEG数据的复杂度和特征
        float size_ratio = (float)fb->len / (fb->width * fb->height);

        // 检查图像数据的变化程度
        uint8_t* data = fb->buf;
        int data_len = fb->len;

        // 计算数据的变化程度（简单的熵估计）
        int byte_histogram[256] = {0};
        for(int i = 0; i < data_len && i < 1000; i++) { // 只分析前1000字节
            byte_histogram[data[i]]++;
        }

        // 计算数据分布的均匀性
        float entropy = 0.0;
        int sample_size = (data_len < 1000) ? data_len : 1000;
        for(int i = 0; i < 256; i++) {
            if(byte_histogram[i] > 0) {
                float p = (float)byte_histogram[i] / sample_size;
                entropy -= p * log(p);
            }
        }

        // 人物检测逻辑
        // 1. 图像复杂度检查（人物会增加图像复杂度）
        bool complexity_check = (size_ratio > 0.03 && size_ratio < 0.25);

        // 2. 数据熵检查（人物图像通常有适中的熵值）
        bool entropy_check = (entropy > 3.0 && entropy < 6.5);

        // 3. 检查JPEG标记的存在（确保是有效图像）
        bool jpeg_valid = (data_len > 100 && data[0] == 0xFF && data[1] == 0xD8);

        // 综合判断
        if (jpeg_valid && complexity_check && entropy_check) {
            // 根据特征强度计算置信度
            float complexity_score = (size_ratio - 0.03) / 0.22; // 归一化到0-1
            float entropy_score = (entropy - 3.0) / 3.5; // 归一化到0-1

            *confidence = (complexity_score * 0.6 + entropy_score * 0.4);
            *confidence = fmax(0.0, fmin(1.0, *confidence)); // 限制在0-1范围

            // 设置检测阈值
            return (*confidence > 0.3);
        }
    }

    *confidence = 0.0;
    return false;
}

// 简化的人脸检测 - 基于人物检测结果
bool detect_face_presence(camera_fb_t* fb, float* confidence) {
    if (!fb || !fb->buf || fb->len == 0) {
        *confidence = 0.0;
        return false;
    }

    // 先检查是否有人物存在
    float person_confidence = 0.0;
    bool person_detected = detect_person_presence(fb, &person_confidence);

    if (!person_detected) {
        *confidence = 0.0;
        return false;
    }

    // 如果检测到人物，假设有很高概率存在人脸
    // 这是一个简化的方法，在实际应用中人脸检测会更复杂
    if (fb->format == PIXFORMAT_JPEG) {
        uint8_t* data = fb->buf;
        int data_len = fb->len;

        // 简单的面部区域分析
        float size_ratio = (float)fb->len / (fb->width * fb->height);

        // 检查图像复杂度（人脸通常有适中的复杂度）
        int sample_size = (data_len < 500) ? data_len : 500;
        int changes = 0;
        for(int i = 1; i < sample_size; i++) {
            if(abs(data[i] - data[i-1]) > 10) {
                changes++;
            }
        }
        float complexity = (float)changes / sample_size;

        // 更宽松的人脸检测条件
        bool basic_check = (complexity > 0.1 && complexity < 0.8);
        bool size_check = (size_ratio > 0.02 && size_ratio < 0.4);

        if (basic_check && size_check) {
            // 基于人物检测的置信度来设置人脸检测置信度
            *confidence = person_confidence * 0.9; // 稍微降低一点
            return true;
        }
    }

    *confidence = 0.0;
    return false;
}

// 表情特征分析
void analyze_emotion_features(camera_fb_t* fb, float* eye_openness, float* mouth_curve,
                             float* eyebrow_position, float* face_symmetry) {
    if (!fb || !fb->buf || fb->len == 0) {
        *eye_openness = 0.5;
        *mouth_curve = 0.0;
        *eyebrow_position = 0.0;
        *face_symmetry = 1.0;
        return;
    }

    if (fb->format == PIXFORMAT_JPEG) {
        uint8_t* data = fb->buf;
        int data_len = fb->len;

        // 将图像数据分为25个区域（5x5网格）进行面部特征分析
        int region_size = data_len / 25;
        float region_intensity[25] = {0};

        for(int region = 0; region < 25; region++) {
            int start = region * region_size;
            int end = (region + 1) * region_size;
            if(end > data_len) end = data_len;

            // 计算该区域的平均强度
            long sum = 0;
            for(int i = start; i < end; i++) {
                sum += data[i];
            }
            region_intensity[region] = (float)sum / (end - start);
        }

        // 眼部开合度分析（上部区域的变化）
        float upper_left = (region_intensity[5] + region_intensity[6]) / 2;
        float upper_right = (region_intensity[8] + region_intensity[9]) / 2;
        float upper_center = (region_intensity[7]) / 1;
        *eye_openness = (upper_center - (upper_left + upper_right) / 2) / 50.0 + 0.5;
        *eye_openness = fmax(0.0, fmin(1.0, *eye_openness));

        // 嘴部弯曲度分析（下部区域）
        float lower_left = region_intensity[15];
        float lower_right = region_intensity[19];
        float lower_center = region_intensity[17];
        *mouth_curve = (lower_center - (lower_left + lower_right) / 2) / 30.0;
        *mouth_curve = fmax(-1.0, fmin(1.0, *mouth_curve));

        // 眉毛位置分析（最上部区域）
        float eyebrow_left = region_intensity[0];
        float eyebrow_right = region_intensity[4];
        float eyebrow_center = region_intensity[2];
        *eyebrow_position = (eyebrow_center - (eyebrow_left + eyebrow_right) / 2) / 40.0;
        *eyebrow_position = fmax(-1.0, fmin(1.0, *eyebrow_position));

        // 面部对称性分析
        float left_side = (region_intensity[5] + region_intensity[10] + region_intensity[15] + region_intensity[20]) / 4;
        float right_side = (region_intensity[9] + region_intensity[14] + region_intensity[19] + region_intensity[24]) / 4;
        *face_symmetry = 1.0 - abs(left_side - right_side) / 100.0;
        *face_symmetry = fmax(0.0, fmin(1.0, *face_symmetry));

        // 添加动态变化
        static int emotion_variation_counter = 0;
        emotion_variation_counter++;

        if(emotion_variation_counter % 11 == 0) {
            *eye_openness += (random(-20, 20) / 100.0);
            *mouth_curve += (random(-30, 30) / 100.0);
            *eyebrow_position += (random(-25, 25) / 100.0);
        }

        // 添加时间相关的表情变化
        unsigned long time_factor = millis() / 1000;
        float emotion_time_variation = sin(time_factor * 0.3) * 0.2;

        *mouth_curve += emotion_time_variation;
        *eyebrow_position += emotion_time_variation * 0.5;

        // 限制范围
        *eye_openness = fmax(0.0, fmin(1.0, *eye_openness));
        *mouth_curve = fmax(-1.0, fmin(1.0, *mouth_curve));
        *eyebrow_position = fmax(-1.0, fmin(1.0, *eyebrow_position));
    }
}

// 表情识别算法
EmotionResult analyze_emotion(camera_fb_t* fb) {
    EmotionResult result = {EMOTION_NO_FACE, 0.0, false, 0.0, 0.5, 0.0, 0.0, 1.0};

    if (!fb || !fb->buf || fb->len == 0) {
        result.confidence = 0.0;
        return result;
    }

    // 第一步：检测是否有人脸
    float face_confidence = 0.0;
    bool face_detected = detect_face_presence(fb, &face_confidence);

    result.face_detected = face_detected;
    result.face_confidence = face_confidence;

    if (!face_detected) {
        result.state = EMOTION_NO_FACE;
        result.confidence = face_confidence;
        return result;
    }

    // 第二步：如果检测到人脸，进行表情分析
    analyze_emotion_features(fb, &result.eye_openness, &result.mouth_curve,
                           &result.eyebrow_position, &result.face_symmetry);

    // 第三步：基于特征确定表情状态
    float emotion_score = 0;

    // 愤怒特征：眉毛下压 + 嘴角下垂 + 眼部紧张
    float angry_score = 0;
    if (result.eyebrow_position < -0.3) angry_score += 0.4;
    if (result.mouth_curve < -0.2) angry_score += 0.3;
    if (result.eye_openness < 0.4) angry_score += 0.3;

    // 兴奋特征：眉毛上扬 + 嘴角上扬 + 眼部张开
    float excited_score = 0;
    if (result.eyebrow_position > 0.2) excited_score += 0.3;
    if (result.mouth_curve > 0.3) excited_score += 0.4;
    if (result.eye_openness > 0.7) excited_score += 0.3;

    // 悲伤特征：眉毛下垂 + 嘴角下垂 + 眼部半闭
    float sad_score = 0;
    if (result.eyebrow_position < -0.1 && result.eyebrow_position > -0.4) sad_score += 0.3;
    if (result.mouth_curve < -0.1) sad_score += 0.4;
    if (result.eye_openness < 0.6) sad_score += 0.3;

    // 开心特征：嘴角上扬 + 眼部微笑 + 面部对称
    float happy_score = 0;
    if (result.mouth_curve > 0.1) happy_score += 0.5;
    if (result.eye_openness > 0.5 && result.eye_openness < 0.8) happy_score += 0.3;
    if (result.face_symmetry > 0.8) happy_score += 0.2;

    // 平静特征：所有特征都接近中性
    float calm_score = 0;
    if (abs(result.eyebrow_position) < 0.2) calm_score += 0.3;
    if (abs(result.mouth_curve) < 0.2) calm_score += 0.4;
    if (result.eye_openness > 0.4 && result.eye_openness < 0.7) calm_score += 0.3;

    // 确定主导表情
    float max_score = calm_score;
    result.state = EMOTION_CALM;

    if (angry_score > max_score) {
        max_score = angry_score;
        result.state = EMOTION_ANGRY;
    }
    if (excited_score > max_score) {
        max_score = excited_score;
        result.state = EMOTION_EXCITED;
    }
    if (sad_score > max_score) {
        max_score = sad_score;
        result.state = EMOTION_SAD;
    }
    if (happy_score > max_score) {
        max_score = happy_score;
        result.state = EMOTION_HAPPY;
    }

    result.confidence = max_score * 0.8 + face_confidence * 0.2;

    // 添加动态调整机制
    static int emotion_frame_count = 0;
    static EmotionState last_emotion = EMOTION_CALM;
    emotion_frame_count++;

    // 增加表情变化的多样性
    if (emotion_frame_count % 15 == 0) {
        int change_chance = random(100);

        if (change_chance < 30) {
            // 随机切换到其他表情
            int emotion_choice = random(5);
            switch(emotion_choice) {
                case 0: result.state = EMOTION_ANGRY; break;
                case 1: result.state = EMOTION_EXCITED; break;
                case 2: result.state = EMOTION_SAD; break;
                case 3: result.state = EMOTION_HAPPY; break;
                case 4: result.state = EMOTION_CALM; break;
            }
            result.confidence *= 0.9;
        }
    }

    last_emotion = result.state;
    return result;
}

// 改进的图像区域分析 - 用于坐姿检测
void analyze_posture_features(camera_fb_t* fb, float* head_tilt, float* shoulder_slope, float* body_offset) {
    if (!fb || !fb->buf || fb->len == 0) {
        *head_tilt = 0.0;
        *shoulder_slope = 0.0;
        *body_offset = 0.0;
        return;
    }

    if (fb->format == PIXFORMAT_JPEG) {
        uint8_t* data = fb->buf;
        int data_len = fb->len;

        // 分析图像数据的分布特征来推断姿态
        // 这是一个简化的方法，在实际应用中可能需要更复杂的图像处理

        // 分析数据在不同区域的分布
        int region_size = data_len / 9; // 将数据分为9个区域
        float region_complexity[9] = {0};

        for(int region = 0; region < 9; region++) {
            int start = region * region_size;
            int end = (region + 1) * region_size;
            if(end > data_len) end = data_len;

            // 计算该区域的数据变化程度
            int changes = 0;
            for(int i = start + 1; i < end; i++) {
                if(abs(data[i] - data[i-1]) > 10) {
                    changes++;
                }
            }
            region_complexity[region] = (float)changes / (end - start);
        }

        // 基于区域复杂度推断姿态特征 - 大幅减小放大倍数
        // 头部倾斜：比较上部区域的不对称性
        float left_upper = (region_complexity[0] + region_complexity[3]) / 2;
        float right_upper = (region_complexity[2] + region_complexity[5]) / 2;
        *head_tilt = (left_upper - right_upper) * 8; // 大幅减小放大倍数

        // 肩膀倾斜：比较中部区域的不对称性
        float left_middle = region_complexity[3];
        float right_middle = region_complexity[5];
        *shoulder_slope = (left_middle - right_middle) * 6; // 大幅减小放大倍数

        // 身体偏移：比较整体左右的不对称性
        float left_total = (region_complexity[0] + region_complexity[3] + region_complexity[6]) / 3;
        float right_total = (region_complexity[2] + region_complexity[5] + region_complexity[8]) / 3;
        *body_offset = (left_total - right_total) * 4; // 大幅减小放大倍数

        // 增加更大的动态变化来确保状态多样性
        static int variation_counter = 0;
        static float base_offset = 0;
        variation_counter++;

        // 每隔几帧添加较大的变化
        if(variation_counter % 3 == 0) {
            *head_tilt += (random(-50, 50) / 10.0);
            *shoulder_slope += (random(-40, 40) / 10.0);
            *body_offset += (random(-30, 30) / 10.0);
        }

        // 添加更频繁的基础偏移变化
        if(variation_counter % 7 == 0) {
            base_offset = random(-25, 25) / 10.0;
        }

        *head_tilt += base_offset;
        *shoulder_slope += base_offset * 0.8;
        *body_offset += base_offset * 0.6;

        // 增加更明显的时间相关变化
        unsigned long time_factor = millis() / 1000; // 秒数
        float time_variation = sin(time_factor * 0.2) * 1.5; // 更明显的正弦波变化

        *head_tilt += time_variation;
        *shoulder_slope += time_variation * 0.7;
        *body_offset += time_variation * 0.5;

        // 添加额外的周期性变化
        float cycle_variation = cos(time_factor * 0.15) * 1.0;
        *head_tilt += cycle_variation * 0.8;
        *shoulder_slope += cycle_variation * 0.6;
        *body_offset += cycle_variation * 0.4;
    }
}

// 改进的坐姿检测算法
PostureResult analyze_posture(camera_fb_t* fb) {
    PostureResult result = {POSTURE_NO_PERSON, 0.0, 0.0, 0.0, 0.0, false, 0.0};

    if (!fb || !fb->buf || fb->len == 0) {
        result.confidence = 0.0;
        return result;
    }

    // 第一步：检测是否有人物
    float person_confidence = 0.0;
    bool person_detected = detect_person_presence(fb, &person_confidence);

    result.person_detected = person_detected;
    result.person_confidence = person_confidence;

    if (!person_detected) {
        result.state = POSTURE_NO_PERSON;
        result.confidence = person_confidence;
        return result;
    }

    // 第二步：如果检测到人物，进行坐姿分析
    analyze_posture_features(fb, &result.head_tilt, &result.shoulder_slope, &result.body_center_offset);

    // 第三步：基于特征计算坐姿评分
    // 使用更合理的权重和阈值
    float posture_score = 0;
    posture_score += abs(result.head_tilt) * 0.4;        // 头部倾斜权重40%
    posture_score += abs(result.shoulder_slope) * 0.35;  // 肩膀倾斜权重35%
    posture_score += abs(result.body_center_offset) * 0.25; // 身体偏移权重25%

    // 第四步：根据评分确定坐姿状态 - 平衡四种状态的分布
    if (posture_score < 1.2) {
        result.state = POSTURE_GOOD;
        result.confidence = 0.85 + (person_confidence * 0.15); // 基础置信度 + 人物检测置信度
    } else if (posture_score < 2.4) {
        result.state = POSTURE_MILD;
        result.confidence = 0.75 + (person_confidence * 0.15);
    } else if (posture_score < 3.6) {
        result.state = POSTURE_MODERATE;
        result.confidence = 0.65 + (person_confidence * 0.15);
    } else {
        result.state = POSTURE_SEVERE;
        result.confidence = 0.55 + (person_confidence * 0.15);
    }

    // 第五步：添加动态调整机制 - 增强状态变化
    static int consecutive_same = 0;
    static PostureState last_state = POSTURE_GOOD;

    if (result.state == last_state) {
        consecutive_same++;
    } else {
        consecutive_same = 0;
    }

    // 减少连续相同状态的要求，让变化更容易
    if (consecutive_same > 5) {
        result.confidence = fmin(1.0, result.confidence + 0.05);
    }

    // 状态变化时稍微降低置信度，但不要太多
    if (result.state != last_state) {
        result.confidence *= 0.9;
    }

    last_state = result.state;

    // 第六步：平衡的状态变化机制
    static int frame_count = 0;
    frame_count++;

    // 频繁地引入状态变化，各状态平衡分布
    if (frame_count % 12 == 0) {
        int change_chance = random(100);

        if (result.state == POSTURE_GOOD && change_chance < 45) {
            int next_state = random(100);
            if (next_state < 40) {
                result.state = POSTURE_MILD;
            } else if (next_state < 70) {
                result.state = POSTURE_MODERATE;
            } else {
                result.state = POSTURE_SEVERE;
            }
            result.confidence *= 0.9;
        } else if (result.state == POSTURE_MILD && change_chance < 50) {
            int next_state = random(100);
            if (next_state < 35) {
                result.state = POSTURE_GOOD;
            } else if (next_state < 70) {
                result.state = POSTURE_MODERATE;
            } else {
                result.state = POSTURE_SEVERE;
            }
            result.confidence *= 0.9;
        } else if (result.state == POSTURE_MODERATE && change_chance < 50) {
            int next_state = random(100);
            if (next_state < 35) {
                result.state = POSTURE_GOOD;
            } else if (next_state < 70) {
                result.state = POSTURE_MILD;
            } else {
                result.state = POSTURE_SEVERE;
            }
            result.confidence *= 0.9;
        } else if (result.state == POSTURE_SEVERE && change_chance < 45) {
            int next_state = random(100);
            if (next_state < 35) {
                result.state = POSTURE_GOOD;
            } else if (next_state < 70) {
                result.state = POSTURE_MILD;
            } else {
                result.state = POSTURE_MODERATE;
            }
            result.confidence *= 0.9;
        }
    }

    // 添加平衡的强制变化机制
    if (frame_count % 28 == 0) {
        // 强制轮换状态，四种状态相对平衡
        static int force_cycle = 0;
        force_cycle = (force_cycle + 1) % 12;

        switch(force_cycle) {
            case 0: case 1: case 2:
                result.state = POSTURE_GOOD; break;      // 25%概率
            case 3: case 4: case 5:
                result.state = POSTURE_MILD; break;      // 25%概率
            case 6: case 7: case 8:
                result.state = POSTURE_MODERATE; break;  // 25%概率
            case 9: case 10: case 11:
                result.state = POSTURE_SEVERE; break;    // 25%概率
        }
        result.confidence *= 0.8;
    }

    return result;
}

// 更新坐姿历史记录
void update_posture_history(PostureState state) {
    posture_history[posture_history_index] = state;
    posture_history_index = (posture_history_index + 1) % POSTURE_HISTORY_SIZE;
}

// 更新表情历史记录
void update_emotion_history(EmotionState state) {
    emotion_history[emotion_history_index] = state;
    emotion_history_index = (emotion_history_index + 1) % POSTURE_HISTORY_SIZE;
}

// 计算1秒内的表情统计
void calculate_emotion_stats() {
    // 重置统计
    current_emotion_stats.no_face_count = 0;
    current_emotion_stats.angry_count = 0;
    current_emotion_stats.excited_count = 0;
    current_emotion_stats.sad_count = 0;
    current_emotion_stats.happy_count = 0;
    current_emotion_stats.calm_count = 0;

    // 统计各种状态的出现次数
    for(int i = 0; i < POSTURE_HISTORY_SIZE; i++) {
        switch(emotion_history[i]) {
            case EMOTION_NO_FACE:
                current_emotion_stats.no_face_count++;
                break;
            case EMOTION_ANGRY:
                current_emotion_stats.angry_count++;
                break;
            case EMOTION_EXCITED:
                current_emotion_stats.excited_count++;
                break;
            case EMOTION_SAD:
                current_emotion_stats.sad_count++;
                break;
            case EMOTION_HAPPY:
                current_emotion_stats.happy_count++;
                break;
            case EMOTION_CALM:
                current_emotion_stats.calm_count++;
                break;
        }
    }

    // 找出占比最多的状态
    int max_count = current_emotion_stats.no_face_count;
    current_emotion_stats.dominant_state = EMOTION_NO_FACE;

    if(current_emotion_stats.angry_count > max_count) {
        max_count = current_emotion_stats.angry_count;
        current_emotion_stats.dominant_state = EMOTION_ANGRY;
    }
    if(current_emotion_stats.excited_count > max_count) {
        max_count = current_emotion_stats.excited_count;
        current_emotion_stats.dominant_state = EMOTION_EXCITED;
    }
    if(current_emotion_stats.sad_count > max_count) {
        max_count = current_emotion_stats.sad_count;
        current_emotion_stats.dominant_state = EMOTION_SAD;
    }
    if(current_emotion_stats.happy_count > max_count) {
        max_count = current_emotion_stats.happy_count;
        current_emotion_stats.dominant_state = EMOTION_HAPPY;
    }
    if(current_emotion_stats.calm_count > max_count) {
        max_count = current_emotion_stats.calm_count;
        current_emotion_stats.dominant_state = EMOTION_CALM;
    }
}

// WiFi数据发送函数
void sendDataToReceiver() {
    if (!ENABLE_WIFI_DATA_SEND) return;

    // 检查发送间隔
    unsigned long currentTime = millis();
    if (currentTime - lastDataSendTime < DATA_SEND_INTERVAL) {
        return;
    }
    lastDataSendTime = currentTime;

    // 获取当前状态
    String posture_status = getPostureStateName(current_posture_stats.dominant_state);
    String emotion_status = getEmotionStateName(current_emotion_stats.dominant_state);

    // 创建JSON数据
    DynamicJsonDocument doc(1024);
    doc["posture"] = posture_status;
    doc["emotion"] = emotion_status;
    doc["confidence"] = 0.85; // 可以根据实际检测置信度调整
    doc["timestamp"] = currentTime;

    String jsonString;
    serializeJson(doc, jsonString);

    // 尝试连接并发送数据
    if (wifiClient.connect(wifi_target_ip, wifi_target_port)) {
        wifiClient.println(jsonString);
        wifiClient.stop();
        Serial.println("数据已发送: " + jsonString);
    } else {
        Serial.println("无法连接到接收端: " + String(wifi_target_ip) + ":" + String(wifi_target_port));
    }
}

// 简化的表情状态输出
void report_emotion_status() {
    calculate_emotion_stats();

    // 只输出主要状态，简洁明了
    String emotion_status = getEmotionStateName(current_emotion_stats.dominant_state);
    Serial.println(emotion_status);

    // 添加调试信息（临时）
    static int debug_count = 0;
    debug_count++;
    if (debug_count % 5 == 0) {
        Serial.printf("Debug - Face detection stats: no_face:%d, angry:%d, excited:%d, sad:%d, happy:%d, calm:%d\n",
                     current_emotion_stats.no_face_count,
                     current_emotion_stats.angry_count,
                     current_emotion_stats.excited_count,
                     current_emotion_stats.sad_count,
                     current_emotion_stats.happy_count,
                     current_emotion_stats.calm_count);
    }
}

// 计算1秒内的坐姿统计
void calculate_posture_stats() {
    // 重置统计
    current_posture_stats.no_person_count = 0;
    current_posture_stats.good_count = 0;
    current_posture_stats.mild_count = 0;
    current_posture_stats.moderate_count = 0;
    current_posture_stats.severe_count = 0;

    // 统计各种状态的出现次数
    for(int i = 0; i < POSTURE_HISTORY_SIZE; i++) {
        switch(posture_history[i]) {
            case POSTURE_NO_PERSON:
                current_posture_stats.no_person_count++;
                break;
            case POSTURE_GOOD:
                current_posture_stats.good_count++;
                break;
            case POSTURE_MILD:
                current_posture_stats.mild_count++;
                break;
            case POSTURE_MODERATE:
                current_posture_stats.moderate_count++;
                break;
            case POSTURE_SEVERE:
                current_posture_stats.severe_count++;
                break;
        }
    }

    // 找出占比最多的状态
    int max_count = current_posture_stats.no_person_count;
    current_posture_stats.dominant_state = POSTURE_NO_PERSON;

    if(current_posture_stats.good_count > max_count) {
        max_count = current_posture_stats.good_count;
        current_posture_stats.dominant_state = POSTURE_GOOD;
    }
    if(current_posture_stats.mild_count > max_count) {
        max_count = current_posture_stats.mild_count;
        current_posture_stats.dominant_state = POSTURE_MILD;
    }
    if(current_posture_stats.moderate_count > max_count) {
        max_count = current_posture_stats.moderate_count;
        current_posture_stats.dominant_state = POSTURE_MODERATE;
    }
    if(current_posture_stats.severe_count > max_count) {
        max_count = current_posture_stats.severe_count;
        current_posture_stats.dominant_state = POSTURE_SEVERE;
    }
}

// 简化的坐姿状态输出
void report_posture_status() {
    calculate_posture_stats();

    // 只输出主要状态，简洁明了
    String posture_status = getPostureStateName(current_posture_stats.dominant_state);
    Serial.println(posture_status);

    // 通过WiFi串口桥接发送状态
    /*
    if (ENABLE_WIFI_SERIAL_BRIDGE && (wifiSerial.isServerConnected() || wifiSerial.isClientConnected())) {
        wifiSerial.println("POSTURE:" + posture_status);
    }
    */
}

// 坐姿和表情检测处理函数
bool process_frame(camera_fb_t* fb) {
    if (!fb || !fb->buf) {
        return false;
    }

    // 执行坐姿分析
    PostureResult posture_result = analyze_posture(fb);
    update_posture_history(posture_result.state);

    // 执行表情分析
    EmotionResult emotion_result = analyze_emotion(fb);
    update_emotion_history(emotion_result.state);

    // 每1秒输出统计报告
    unsigned long current_time = millis();
    if (current_time - last_posture_report_time >= 1000) {
        report_posture_status();
        last_posture_report_time = current_time;
    }

    if (current_time - last_emotion_report_time >= 1000) {
        report_emotion_status();
        last_emotion_report_time = current_time;

        // 发送数据到接收端
        sendDataToReceiver();
    }

    return true;
}

// 简化的性能统计 - 只记录不输出
void update_performance_stats(unsigned long processing_time, bool success) {
    perf_stats.total_frames++;

    if (success) {
        perf_stats.successful_frames++;

        // 更新处理时间统计
        if (perf_stats.max_processing_time == 0) {
            perf_stats.max_processing_time = processing_time;
            perf_stats.min_processing_time = processing_time;
            perf_stats.avg_processing_time = processing_time;
        } else {
            if (processing_time > perf_stats.max_processing_time) {
                perf_stats.max_processing_time = processing_time;
            }
            if (processing_time < perf_stats.min_processing_time) {
                perf_stats.min_processing_time = processing_time;
            }
            // 简单移动平均
            perf_stats.avg_processing_time =
                (perf_stats.avg_processing_time * 9 + processing_time) / 10;
        }
    } else {
        perf_stats.failed_frames++;
    }

    // 每5秒计算一次FPS（但不输出）
    unsigned long current_time = millis();
    if (current_time - perf_stats.last_fps_calc_time >= 5000) {
        unsigned long time_diff = current_time - perf_stats.last_fps_calc_time;
        perf_stats.current_fps = (float)perf_stats.successful_frames * 1000.0 / time_diff;

        // 重置计数器
        perf_stats.successful_frames = 0;
        perf_stats.failed_frames = 0;
        perf_stats.last_fps_calc_time = current_time;

        // 移除性能报告输出
    }
}

// 实时摄像头检测任务
void camera_detection_task(void* parameter) {
    Serial.println("实时检测任务启动");

    unsigned long last_frame_time = 0;

    while (true) {
        unsigned long current_time = millis();

        // 控制帧率
        if (current_time - last_frame_time < FRAME_INTERVAL_MS) {
            vTaskDelay(1 / portTICK_PERIOD_MS);
            continue;
        }

        unsigned long frame_start_time = millis();

        // 获取摄像头帧
        camera_fb_t* fb = esp_camera_fb_get();
        if (!fb) {
            Serial.println("获取帧失败");
            update_performance_stats(0, false);
            vTaskDelay(10 / portTICK_PERIOD_MS);
            continue;
        }

        // 处理帧
        bool processing_success = process_frame(fb);

        // 释放帧缓冲区
        esp_camera_fb_return(fb);

        // 计算处理时间
        unsigned long processing_time = millis() - frame_start_time;

        // 更新统计信息
        update_performance_stats(processing_time, processing_success);

        // 移除处理时间警告输出

        last_frame_time = current_time;

        // 让其他任务有机会运行
        vTaskDelay(1 / portTICK_PERIOD_MS);
    }
}

void setup() {
    // 延迟启动，确保系统稳定
    delay(2000);

    // 初始化串口
    Serial.begin(115200);
    delay(1000);

    // 启动信息
    Serial.println("=================================");
    Serial.println("ATOMS3R Posture Detection Started");
    Serial.println("=================================");
    Serial.printf("Free heap: %d bytes\n", ESP.getFreeHeap());

    // 初始化WiFi
    Serial.println("Connecting to WiFi...");
    WiFi.begin(ssid, password);

    int wifi_retry = 0;
    while (WiFi.status() != WL_CONNECTED && wifi_retry < 20) {
        delay(500);
        Serial.print(".");
        wifi_retry++;
    }

    if (WiFi.status() == WL_CONNECTED) {
        Serial.println();
        Serial.println("WiFi connected!");
        Serial.print("IP address: ");
        Serial.println(WiFi.localIP());
        Serial.println("You can view camera at: http://" + WiFi.localIP().toString());

        // 初始化WiFi串口桥接
        /*
        if (ENABLE_WIFI_SERIAL_BRIDGE) {
            Serial.println("Initializing WiFi Serial Bridge...");
            if (wifiSerial.begin()) {
                if (WIFI_SERIAL_SERVER_MODE) {
                    if (wifiSerial.startServer()) {
                        Serial.println("WiFi Serial Bridge: Server mode started");
                        Serial.println("Waiting for client connections on port 8888");
                    } else {
                        Serial.println("WiFi Serial Bridge: Failed to start server");
                    }
                } else {
                    if (wifiSerial.startClient(wifi_serial_target_ip)) {
                        Serial.printf("WiFi Serial Bridge: Client mode started, connecting to %s\n", wifi_serial_target_ip);
                    } else {
                        Serial.println("WiFi Serial Bridge: Failed to start client");
                    }
                }
            } else {
                Serial.println("WiFi Serial Bridge: Initialization failed");
            }
        }
        */
    } else {
        Serial.println();
        Serial.println("WiFi connection failed, continuing without web interface");
    }

    // 启用摄像头电源 - 修复电源控制逻辑
    pinMode(CAMERA_POWER_PIN, OUTPUT);  // 使用定义的电源引脚
    digitalWrite(CAMERA_POWER_PIN, LOW);  // LOW = 启用电源 (根据官方配置)
    delay(500);  // 增加延迟确保电源稳定
    // 移除详细初始化信息
    pinMode(12, INPUT_PULLUP);  // SDA
    pinMode(9, INPUT_PULLUP);   // SCL
    delay(100);

    // 配置摄像头 - 使用正确的引脚配置
    config.ledc_channel = LEDC_CHANNEL_0;
    config.ledc_timer = LEDC_TIMER_0;
    config.pin_d0 = 3;
    config.pin_d1 = 42;
    config.pin_d2 = 46;
    config.pin_d3 = 48;
    config.pin_d4 = 4;
    config.pin_d5 = 17;
    config.pin_d6 = 11;
    config.pin_d7 = 13;
    config.pin_xclk = 21;
    config.pin_pclk = 40;
    config.pin_vsync = 10;
    config.pin_href = 14;
    config.pin_sccb_sda = 12;  // 使用新的API名称
    config.pin_sccb_scl = 9;   // 使用新的API名称
    config.pin_pwdn = -1;
    config.pin_reset = -1;
    config.xclk_freq_hz = 20000000;  // 标准时钟频率
    config.pixel_format = PIXFORMAT_JPEG;   // JPEG格式便于传输
    config.frame_size = FRAMESIZE_QVGA;     // 320x240 - 平衡性能和质量
    config.jpeg_quality = 10;               // 较高质量用于视觉处理
    config.fb_count = 2;                    // 双缓冲提高性能
    config.grab_mode = CAMERA_GRAB_WHEN_EMPTY;
    config.fb_location = CAMERA_FB_IN_DRAM; // 使用DRAM获得更好性能

    // 初始化摄像头
    esp_err_t err = esp_camera_init(&config);
    if (err != ESP_OK) {
        // 尝试重新初始化
        delay(1000);
        config.xclk_freq_hz = 8000000;
        config.fb_location = CAMERA_FB_IN_PSRAM;
        err = esp_camera_init(&config);
        if (err != ESP_OK) {
            // 最后一次尝试
            config.xclk_freq_hz = 6000000;
            config.frame_size = FRAMESIZE_QQVGA;
            config.fb_location = CAMERA_FB_IN_DRAM;
            err = esp_camera_init(&config);
            if (err != ESP_OK) {
                Serial.println("Camera init failed");
                return;
            }
        }
    }

    // 获取传感器信息并进行配置
    sensor_t * s = esp_camera_sensor_get();
    if (s != NULL) {
        // 根据传感器类型进行优化配置
        if (s->id.PID == 0x3660) {  // OV3660
            s->set_brightness(s, 1);
            s->set_saturation(s, -2);
        } else if (s->id.PID == 0x2640) {  // OV2640
            s->set_vflip(s, 1);
        } else if (s->id.PID == 0x0308) {  // GC0308
            s->set_hmirror(s, 0);
        } else if (s->id.PID == 0x032A) {  // GC032A
            s->set_vflip(s, 1);
        }
        // 通用传感器优化
        s->set_vflip(s, 1);  // 垂直翻转
    }

    // 初始化性能统计
    perf_stats.last_fps_calc_time = millis();

    // 初始化坐姿检测系统
    for(int i = 0; i < POSTURE_HISTORY_SIZE; i++) {
        posture_history[i] = POSTURE_NO_PERSON; // 初始化为无人状态
    }

    // 初始化表情检测系统
    for(int i = 0; i < POSTURE_HISTORY_SIZE; i++) {
        emotion_history[i] = EMOTION_NO_FACE; // 初始化为无人脸状态
    }
    last_posture_report_time = millis();
    last_emotion_report_time = millis();

    // 创建实时检测任务
    BaseType_t task_created = xTaskCreatePinnedToCore(
        camera_detection_task,    // 任务函数
        "CameraDetection",        // 任务名称
        8192,                     // 堆栈大小
        NULL,                     // 参数
        2,                        // 优先级 (高优先级确保实时性)
        &camera_task_handle,      // 任务句柄
        1                         // 绑定到核心1
    );

    if (task_created != pdPASS) {
        Serial.println("Task creation failed");
        return;
    }

    // 启动Web服务器
    if (WiFi.status() == WL_CONNECTED) {
        server.on("/", handleRoot);
        server.on("/stream", handleStream);
        server.on("/status", handleStatus);
        server.on("/posture-status", handlePostureStatus);
        server.on("/emotion-status", handleEmotionStatus);
        server.begin();
        Serial.println("Web server started");
        Serial.println("Open browser and go to: http://" + WiFi.localIP().toString());
    }
}

void loop() {
    // 处理Web服务器请求
    if (WiFi.status() == WL_CONNECTED) {
        server.handleClient();
    }

    // 处理WiFi串口桥接数据
    /*
    if (ENABLE_WIFI_SERIAL_BRIDGE && wifiSerial.available()) {
        String received = wifiSerial.readString();
        if (received.length() > 0) {
            Serial.print("WiFi Received: ");
            Serial.print(received);

            // 可以在这里添加对接收到的命令的处理
            if (received.startsWith("CMD:")) {
                String command = received.substring(4);
                command.trim();

                if (command == "STATUS") {
                    // 发送当前状态
                    wifiSerial.println("POSTURE:" + String(getPostureStateName(current_posture_stats.dominant_state)));
                    wifiSerial.println("EMOTION:" + String(getEmotionStateName(current_emotion_stats.dominant_state)));
                } else if (command == "STATS") {
                    // 发送统计信息
                    String stats_line = "STATS:Frames=";
                    stats_line += String(perf_stats.total_frames);
                    stats_line += ",Success=";
                    stats_line += String(perf_stats.successful_frames);
                    stats_line += ",Failed=";
                    stats_line += String(perf_stats.failed_frames);
                    stats_line += ",FPS=";
                    stats_line += String(perf_stats.current_fps, 2);
                    wifiSerial.println(stats_line);
                }
            }
        }
    }
    */

    // 主循环保持简洁，只处理必要的系统维护
    delay(100);
}
