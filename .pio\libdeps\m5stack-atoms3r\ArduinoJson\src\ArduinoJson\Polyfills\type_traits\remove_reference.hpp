// Arduino<PERSON>son - https://arduinojson.org
// Copyright © 2014-2025, Benoit BLANCHON
// MIT License

#pragma once

#include <ArduinoJson/Namespace.hpp>

ARDUINOJSON_BEGIN_PRIVATE_NAMESPACE

// A meta-function that return the type T without the reference modifier.
template <typename T>
struct remove_reference {
  using type = T;
};
template <typename T>
struct remove_reference<T&> {
  using type = T;
};

template <typename T>
using remove_reference_t = typename remove_reference<T>::type;

ARDUINOJSON_END_PRIVATE_NAMESPACE
