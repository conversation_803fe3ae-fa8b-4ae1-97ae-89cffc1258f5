/*
 * 简化的蓝牙测试代码
 * 可以用于快速测试蓝牙连接功能
 * 编译时请将此文件重命名为main.cpp
 */

#include <Arduino.h>
#include "BLEDevice.h"
#include "BLEServer.h"
#include "BLEUtils.h"
#include "BLE2902.h"

// 配置选项 - 修改这里来选择发送端或接收端
#define IS_SENDER true  // true=发送端(PICO), false=接收端(DevKitC)

// 蓝牙配置
#define SERVICE_UUID        "12345678-1234-1234-1234-123456789abc"
#define CHARACTERISTIC_UUID "*************-4321-4321-cba987654321"

// 全局变量
BLEServer* pServer = NULL;
BLECharacteristic* pCharacteristic = NULL;
bool deviceConnected = false;
unsigned long lastSendTime = 0;

#if IS_SENDER
// ==================== 发送端代码 ====================

class MyServerCallbacks: public BLEServerCallbacks {
    void onConnect(BLEServer* pServer) {
        deviceConnected = true;
        Serial.println("✅ 设备已连接");
    }

    void onDisconnect(BLEServer* pServer) {
        deviceConnected = false;
        Serial.println("❌ 设备已断开连接");
        // 重新开始广播
        delay(500);
        pServer->startAdvertising();
        Serial.println("🔄 重新开始广播");
    }
};

void setupSender() {
    Serial.println("🚀 初始化蓝牙发送端...");
    
    BLEDevice::init("ESP32-PICO-Test");
    pServer = BLEDevice::createServer();
    pServer->setCallbacks(new MyServerCallbacks());

    BLEService *pService = pServer->createService(SERVICE_UUID);
    pCharacteristic = pService->createCharacteristic(
                         CHARACTERISTIC_UUID,
                         BLECharacteristic::PROPERTY_READ |
                         BLECharacteristic::PROPERTY_WRITE |
                         BLECharacteristic::PROPERTY_NOTIFY
                       );

    pCharacteristic->addDescriptor(new BLE2902());
    pService->start();

    BLEAdvertising *pAdvertising = BLEDevice::getAdvertising();
    pAdvertising->addServiceUUID(SERVICE_UUID);
    pAdvertising->setScanResponse(false);
    pAdvertising->setMinPreferred(0x0);
    BLEDevice::startAdvertising();
    
    Serial.println("📡 蓝牙发送端已启动");
    Serial.println("📱 设备名称: ESP32-PICO-Test");
    Serial.println("⏳ 等待连接...");
}

void loopSender() {
    if (deviceConnected && (millis() - lastSendTime > 2000)) {
        // 每2秒发送一次简单的测试数据
        String testData = "Hello from PICO! Time: " + String(millis());
        pCharacteristic->setValue(testData.c_str());
        pCharacteristic->notify();
        
        Serial.println("📤 发送数据: " + testData);
        lastSendTime = millis();
    }
    
    // 状态指示
    static unsigned long lastStatusTime = 0;
    if (millis() - lastStatusTime > 5000) {
        Serial.printf("🔗 连接状态: %s | 内存: %d bytes\n", 
                     deviceConnected ? "已连接" : "未连接", 
                     ESP.getFreeHeap());
        lastStatusTime = millis();
    }
}

#else
// ==================== 接收端代码 ====================

#include "BLEScan.h"
#include "BLEAdvertisedDevice.h"
#include "BLEClient.h"

static boolean doConnect = false;
static boolean connected = false;
static boolean doScan = false;
static BLERemoteCharacteristic* pRemoteCharacteristic;
static BLEAdvertisedDevice* myDevice;
static BLEClient* pClient;

// 数据接收回调
static void notifyCallback(
  BLERemoteCharacteristic* pBLERemoteCharacteristic,
  uint8_t* pData,
  size_t length,
  bool isNotify) {
    
    String receivedData = "";
    for (int i = 0; i < length; i++) {
        receivedData += (char)pData[i];
    }
    
    Serial.println("📥 接收到数据: " + receivedData);
    
    // 简单的LED指示
    digitalWrite(LED_BUILTIN, !digitalRead(LED_BUILTIN));
}

class MyClientCallback : public BLEClientCallbacks {
  void onConnect(BLEClient* pclient) {
    Serial.println("✅ 客户端已连接");
  }

  void onDisconnect(BLEClient* pclient) {
    connected = false;
    Serial.println("❌ 客户端已断开连接");
  }
};

bool connectToServer() {
    Serial.println("🔗 正在连接到服务器...");
    
    pClient = BLEDevice::createClient();
    pClient->setClientCallbacks(new MyClientCallback());

    pClient->connect(myDevice);
    Serial.println("✅ 已连接到服务器");

    BLERemoteService* pRemoteService = pClient->getService(SERVICE_UUID);
    if (pRemoteService == nullptr) {
        Serial.println("❌ 未找到服务");
        pClient->disconnect();
        return false;
    }

    pRemoteCharacteristic = pRemoteService->getCharacteristic(CHARACTERISTIC_UUID);
    if (pRemoteCharacteristic == nullptr) {
        Serial.println("❌ 未找到特征");
        pClient->disconnect();
        return false;
    }

    if(pRemoteCharacteristic->canNotify())
        pRemoteCharacteristic->registerForNotify(notifyCallback);

    connected = true;
    Serial.println("🎉 连接成功！开始接收数据...");
    return true;
}

class MyAdvertisedDeviceCallbacks: public BLEAdvertisedDeviceCallbacks {
    void onResult(BLEAdvertisedDevice advertisedDevice) {
        Serial.println("🔍 发现设备: " + String(advertisedDevice.toString().c_str()));

        if (advertisedDevice.haveServiceUUID() && 
            advertisedDevice.isAdvertisingService(BLEUUID(SERVICE_UUID))) {
            BLEDevice::getScan()->stop();
            myDevice = new BLEAdvertisedDevice(advertisedDevice);
            doConnect = true;
            doScan = true;
            Serial.println("🎯 找到目标设备！");
        }
    }
};

void setupReceiver() {
    Serial.println("🚀 初始化蓝牙接收端...");
    
    pinMode(LED_BUILTIN, OUTPUT);
    digitalWrite(LED_BUILTIN, LOW);
    
    BLEDevice::init("ESP32-DevKitC-Test");

    BLEScan* pBLEScan = BLEDevice::getScan();
    pBLEScan->setAdvertisedDeviceCallbacks(new MyAdvertisedDeviceCallbacks());
    pBLEScan->setInterval(1349);
    pBLEScan->setWindow(449);
    pBLEScan->setActiveScan(true);
    pBLEScan->start(5, false);
    
    Serial.println("🔍 开始扫描蓝牙设备...");
}

void loopReceiver() {
    if (doConnect == true) {
        if (connectToServer()) {
            Serial.println("🎉 连接成功！");
        } else {
            Serial.println("❌ 连接失败");
        }
        doConnect = false;
    }

    if (!connected && doScan) {
        BLEDevice::getScan()->start(0);
    }
    
    // 状态指示
    static unsigned long lastStatusTime = 0;
    if (millis() - lastStatusTime > 10000) {
        Serial.printf("🔗 连接状态: %s | 内存: %d bytes\n", 
                     connected ? "已连接" : "未连接", 
                     ESP.getFreeHeap());
        lastStatusTime = millis();
    }
}

#endif

// ==================== 主程序 ====================

void setup() {
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("=================================");
    #if IS_SENDER
    Serial.println("📡 ESP32-S3 蓝牙发送端测试");
    Serial.println("📝 设备角色: PICO (发送端)");
    #else
    Serial.println("📡 ESP32-S3 蓝牙接收端测试");
    Serial.println("📝 设备角色: DevKitC (接收端)");
    #endif
    Serial.println("=================================");
    
    #if IS_SENDER
    setupSender();
    #else
    setupReceiver();
    #endif
    
    Serial.println("✅ 系统初始化完成");
}

void loop() {
    #if IS_SENDER
    loopSender();
    #else
    loopReceiver();
    #endif
    
    delay(100);
}
