# WiFi串口桥接系统使用说明

## 概述

这个WiFi串口桥接系统允许两个ESP32-S3R-PICO-1设备通过WiFi网络相互发送串口数据。系统支持服务器-客户端模式和双向通信模式。

## 功能特性

- ✅ **双向通信**：支持两个ESP32之间的双向数据传输
- ✅ **多种模式**：服务器模式、客户端模式、双向模式
- ✅ **数据完整性**：包含校验和验证，确保数据传输可靠
- ✅ **心跳机制**：自动检测连接状态，支持断线重连
- ✅ **队列缓冲**：使用FreeRTOS队列进行数据缓冲
- ✅ **多任务处理**：独立的发送、接收和串口处理任务
- ✅ **统计信息**：提供详细的传输统计和状态监控

## 文件结构

```
src/
├── wifi_serial_bridge.h        # WiFi串口桥接类头文件
├── wifi_serial_bridge.cpp      # WiFi串口桥接类实现
├── wifi_serial_example.cpp     # 独立使用示例
└── main.cpp                     # 集成到摄像头检测系统
```

## 配置说明

### 1. 基本WiFi配置

在 `main.cpp` 中修改WiFi设置：

```cpp
const char* ssid = "ESP32Test";           // WiFi网络名称
const char* password = "12345678";        // WiFi密码
```

### 2. WiFi串口桥接配置

```cpp
#define ENABLE_WIFI_SERIAL_BRIDGE true   // 启用WiFi串口桥接
#define WIFI_SERIAL_SERVER_MODE true     // true=服务器模式, false=客户端模式
const char* wifi_serial_target_ip = "*************";  // 客户端模式时的目标IP
```

### 3. 高级配置

在 `wifi_serial_bridge.h` 中可以修改：

```cpp
#define SERIAL_BRIDGE_PORT 8888          // 通信端口
#define SERIAL_BUFFER_SIZE 1024          // 数据缓冲区大小
#define MAX_CLIENTS 2                    // 最大客户端连接数
#define RECONNECT_DELAY_MS 5000          // 重连延迟
#define HEARTBEAT_INTERVAL_MS 10000      // 心跳间隔
```

## 使用方法

### 方法1：独立使用（推荐用于测试）

1. 将 `wifi_serial_example.cpp` 重命名为 `main.cpp`
2. 配置设备角色：
   ```cpp
   #define CURRENT_DEVICE_ROLE DEVICE_ROLE_SERVER  // 或 DEVICE_ROLE_CLIENT
   ```
3. 编译并上传到两个ESP32设备

### 方法2：集成到现有项目

现有的 `main.cpp` 已经集成了WiFi串口桥接功能：

1. 修改配置参数
2. 编译上传到第一个ESP32（服务器模式）
3. 修改 `WIFI_SERIAL_SERVER_MODE` 为 `false`，设置正确的目标IP
4. 编译上传到第二个ESP32（客户端模式）

## 通信协议

### 数据包格式

```cpp
struct SerialPacket {
    uint8_t type;           // 数据包类型
    uint16_t length;        // 数据长度
    uint32_t timestamp;     // 时间戳
    uint8_t data[1024];     // 数据内容
    uint16_t checksum;      // 校验和
};
```

### 数据包类型

- `PACKET_SERIAL_DATA (0x01)`：串口数据
- `PACKET_HEARTBEAT (0x02)`：心跳包
- `PACKET_ACK (0x03)`：确认包
- `PACKET_STATUS (0x04)`：状态包

### 命令格式

系统支持以下命令（通过串口发送）：

- `CMD:STATUS` - 请求当前状态
- `CMD:STATS` - 请求统计信息
- `status` - 显示连接状态
- `stats` - 显示传输统计

## API接口

### 基本操作

```cpp
// 初始化
wifiSerial.begin();

// 启动服务器模式
wifiSerial.startServer(8888);

// 启动客户端模式
wifiSerial.startClient("*************", 8888);

// 双向模式
wifiSerial.startBidirectional("*************", 8888);
```

### 数据发送

```cpp
// 发送字符串
wifiSerial.write("Hello World");
wifiSerial.println("Hello with newline");

// 发送二进制数据
uint8_t data[] = {0x01, 0x02, 0x03};
wifiSerial.write(data, sizeof(data));
```

### 数据接收

```cpp
// 检查可用数据
if (wifiSerial.available()) {
    String received = wifiSerial.readString();
    Serial.println("Received: " + received);
}
```

### 状态查询

```cpp
// 连接状态
bool serverConnected = wifiSerial.isServerConnected();
bool clientConnected = wifiSerial.isClientConnected();

// 统计信息
unsigned long bytesSent = wifiSerial.getBytesSent();
unsigned long bytesReceived = wifiSerial.getBytesReceived();
```

## 测试步骤

### 1. 准备两个ESP32设备

- ESP32-A：配置为服务器模式
- ESP32-B：配置为客户端模式

### 2. 网络配置

确保两个设备连接到同一个WiFi网络，并记录各自的IP地址。

### 3. 上传代码

1. 上传服务器代码到ESP32-A
2. 修改客户端配置，上传到ESP32-B

### 4. 测试通信

1. 打开两个串口监视器
2. 在任一设备的串口中输入消息
3. 观察另一设备是否收到消息

### 5. 状态监控

- 输入 `status` 查看连接状态
- 输入 `stats` 查看传输统计
- 发送 `CMD:STATUS` 请求对方状态
