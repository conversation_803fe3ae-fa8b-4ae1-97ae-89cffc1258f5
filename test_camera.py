#!/usr/bin/env python3
"""
ATOMS3R-CAM 测试脚本
用于验证摄像头功能是否正常工作
"""

import serial
import time
import sys

def test_camera_serial(port='COM3', baudrate=115200):
    """
    通过串口测试摄像头功能
    
    Args:
        port: 串口端口号 (Windows: COM3, Linux: /dev/ttyUSB0)
        baudrate: 波特率
    """
    try:
        # 连接串口
        ser = serial.Serial(port, baudrate, timeout=1)
        print(f"已连接到 {port}，波特率 {baudrate}")
        print("等待设备启动...")
        time.sleep(3)
        
        # 清空缓冲区
        ser.flushInput()
        ser.flushOutput()
        
        print("\n=== 开始测试摄像头功能 ===")
        
        # 测试命令列表
        test_commands = [
            ('s', '获取摄像头状态'),
            ('c', '捕获图像'),
            ('r', '重置摄像头'),
            ('c', '再次捕获图像')
        ]
        
        for cmd, description in test_commands:
            print(f"\n发送命令: '{cmd}' - {description}")
            ser.write(cmd.encode())
            time.sleep(2)
            
            # 读取响应
            response = ""
            start_time = time.time()
            while time.time() - start_time < 3:  # 3秒超时
                if ser.in_waiting > 0:
                    data = ser.read(ser.in_waiting).decode('utf-8', errors='ignore')
                    response += data
                    print(data, end='')
                time.sleep(0.1)
            
            if not response.strip():
                print("警告: 没有收到响应")
        
        print("\n\n=== 测试完成 ===")
        print("如果看到图像捕获成功的信息，说明摄像头工作正常！")
        
    except serial.SerialException as e:
        print(f"串口错误: {e}")
        print("请检查:")
        print("1. 设备是否正确连接")
        print("2. 串口端口号是否正确")
        print("3. 设备驱动是否安装")
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    finally:
        if 'ser' in locals() and ser.is_open:
            ser.close()
            print("串口已关闭")

def main():
    print("ATOMS3R-CAM 摄像头测试工具")
    print("=" * 40)
    
    # 检查参数
    if len(sys.argv) > 1:
        port = sys.argv[1]
    else:
        port = input("请输入串口端口号 (默认 COM3): ").strip() or "COM3"
    
    print(f"使用串口: {port}")
    print("确保设备已连接并上传了固件...")
    input("按回车键开始测试...")
    
    test_camera_serial(port)

if __name__ == "__main__":
    main()
