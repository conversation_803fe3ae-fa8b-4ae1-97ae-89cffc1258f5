# M5Stack ATOMS3R-CAM PlatformIO Demo

这是一个将ESP-IDF版本的ATOMS3R摄像头demo迁移到PlatformIO的项目。

## 项目特点

- ✅ 支持M5Stack ATOMS3R-CAM摄像头模块
- ✅ 基于Arduino框架，易于开发和调试
- ✅ 简化的代码结构，专注于摄像头功能
- ✅ 串口命令控制和状态监控
- ✅ 自动图像捕获测试功能

## 硬件要求

- M5Stack ATOMS3R-CAM 开发板
- USB-C 数据线
- 电脑（支持PlatformIO IDE）

## 软件要求

- PlatformIO IDE (推荐使用VSCode + PlatformIO插件)
- ESP32-S3 开发环境

## 快速开始

### 1. 克隆或下载项目

将项目文件复制到您的工作目录。

### 2. 打开项目

在PlatformIO IDE中打开项目文件夹。

### 3. 编译项目

```bash
pio run
```

### 4. 上传到设备

```bash
pio run --target upload
```

### 5. 监控串口输出

```bash
pio device monitor
```

## 使用说明

### 串口命令

程序启动后，可以通过串口发送以下命令：

- `c` 或 `C` - 手动捕获图像
- `s` 或 `S` - 显示摄像头状态
- `r` 或 `R` - 重置摄像头

### 按钮控制

- **按钮A** - 捕获图像

### 自动功能

- 程序会每5秒自动捕获一次图像进行测试
- 实时显示图像信息（大小、分辨率等）

## 摄像头配置

当前配置：
- **分辨率**: SVGA (800x600)
- **格式**: JPEG
- **质量**: 12 (0-63，数值越小质量越高)
- **时钟频率**: 20MHz
- **帧缓冲区**: 2个

可以在 `src/main.cpp` 的 `setup()` 函数中修改这些参数。

## 引脚定义

摄像头引脚配置在 `src/camera_pins.h` 中定义：

```cpp
// 数据引脚
#define CAMERA_PIN_D0      3
#define CAMERA_PIN_D1      42
// ... 其他引脚

// I2C通信
#define CAMERA_PIN_SIOD    12  // SDA
#define CAMERA_PIN_SIOC    9   // SCL

// 时钟和同步
#define CAMERA_PIN_XCLK    21
#define CAMERA_PIN_PCLK    40
#define CAMERA_PIN_VSYNC   10
#define CAMERA_PIN_HREF    14
```

## 故障排除

### 摄像头初始化失败

1. **检查硬件连接**
   - 确保摄像头模块正确连接
   - 检查所有引脚连接

2. **检查电源**
   - 确保USB供电充足
   - 检查摄像头电源引脚(GPIO18)

3. **检查配置**
   - 验证 `platformio.ini` 中的配置
   - 确认引脚定义正确

### 编译错误

1. **更新PlatformIO**
   ```bash
   pio upgrade
   ```

2. **清理项目**
   ```bash
   pio run --target clean
   ```

3. **重新安装依赖**
   ```bash
   pio lib install
   ```

## 扩展功能

基于这个基础项目，您可以添加：

- Web服务器功能（实时图像流）
- WiFi连接和远程控制
- 图像处理和分析
- 存储功能（SD卡或云存储）
- 更多传感器集成（IMU、红外等）

## 许可证

本项目基于MIT许可证开源。

## 支持

如果遇到问题，请检查：
1. 硬件连接是否正确
2. PlatformIO环境是否正确安装
3. 串口监控输出的错误信息

## 项目文件结构

```
ATOMS3R-CAM-M12-UserDemo/
├── platformio.ini          # PlatformIO配置文件
├── src/
│   ├── main.cpp            # 主程序
│   ├── camera_init.cpp     # 摄像头初始化实现
│   ├── camera_init.h       # 摄像头初始化头文件
│   └── camera_pins.h       # 引脚定义
├── test_camera.py          # Python测试脚本
└── README_PlatformIO.md    # 使用说明
```

## 与原ESP-IDF版本的区别

1. **框架**: 从ESP-IDF改为Arduino框架，更易于开发
2. **简化**: 移除了复杂的Web服务器和UVC功能，专注于摄像头
3. **依赖**: 使用PlatformIO库管理器，自动处理依赖关系
4. **调试**: 更好的串口调试和状态监控

## 测试方法

### 方法1: 使用Python测试脚本

```bash
# 安装依赖
pip install pyserial

# 运行测试
python test_camera.py
```

### 方法2: 手动串口测试

1. 打开串口监控工具 (如PuTTY, Arduino IDE串口监视器)
2. 连接到设备 (115200波特率)
3. 发送命令测试功能

## 更新日志

- v1.0.0 - 初始版本，基本摄像头功能
- 支持图像捕获和状态监控
- 简化的Arduino框架实现
- 编译成功，RAM使用8.4%，Flash使用7.6%
