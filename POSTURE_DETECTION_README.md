# ATOMS3R 姿态检测系统使用说明

## 功能概述

这个项目将 ATOMS3R-CAM-M12 设备改造为一个智能姿态检测系统，具有以下功能：

### 核心功能
1. **实时人脸检测** - 使用ESP32-S3的AI加速功能
2. **情绪识别** - 检测愤怒、兴奋、悲伤、快乐、平静等情绪状态
3. **姿态分析** - 基于人脸位置和表情分析用户姿态
4. **Web界面** - 通过浏览器查看实时视频流
5. **WiFi串口桥接** - 远程调试和控制功能

### 检测状态说明
- **No Face** - 未检测到人脸
- **No People** - 未检测到人员
- **情绪统计** - 每10次检测输出一次统计信息：
  - `no_face`: 未检测到人脸的次数
  - `angry`: 愤怒表情次数
  - `excited`: 兴奋表情次数  
  - `sad`: 悲伤表情次数
  - `happy`: 快乐表情次数
  - `calm`: 平静表情次数

## 配置说明

### 1. WiFi配置
在 `src/main.cpp` 文件中修改WiFi设置：

```cpp
// WiFi配置 - 请修改为您的实际WiFi网络
const char* ssid = "your_wifi_name";
const char* password = "your_wifi_password";
```

### 2. WiFi串口桥接配置
在 `src/main.cpp` 文件中可以配置串口桥接功能：

```cpp
// WiFi串口桥接配置
const bool ENABLE_WIFI_SERIAL_BRIDGE = false;  // 启用/禁用WiFi串口桥接
const bool WIFI_SERIAL_SERVER_MODE = true;     // true=服务器模式, false=客户端模式
const int WIFI_SERIAL_PORT = 8888;             // 服务器端口
const char* wifi_serial_target_ip = "*************";  // 客户端模式目标IP
```

## 编译和上传

### 使用PlatformIO
1. 确保已安装PlatformIO
2. 在项目目录中运行：
   ```bash
   pio run                    # 编译
   pio run --target upload    # 上传到设备
   pio device monitor         # 监控串口输出
   ```

### 查看运行状态
上传成功后，设备会自动重启并显示：
```
=================================
ATOMS3R Posture Detection Started
=================================
Free heap: XXXXX bytes
Connecting to WiFi...
```

如果WiFi连接成功，会显示：
```
WiFi connected!
IP address: 192.168.x.x
You can view camera at: http://192.168.x.x
```

## 使用方法

### 1. 查看实时视频
- 连接WiFi后，在浏览器中访问设备显示的IP地址
- 例如：`http://*************`

### 2. 监控检测结果
- 通过串口监控查看实时检测结果
- 使用 `pio device monitor` 命令

### 3. WiFi串口桥接（可选）
如果启用了WiFi串口桥接功能：
- 服务器模式：设备在8888端口等待连接
- 客户端模式：设备连接到指定的目标IP

## 故障排除

### 1. WiFi连接失败
- 检查WiFi名称和密码是否正确
- 确保设备在WiFi信号覆盖范围内
- 检查WiFi网络是否支持2.4GHz频段

### 2. 编译错误
- 确保PlatformIO环境正确安装
- 检查是否有多个main.cpp文件冲突
- 清理构建缓存：`pio run --target clean`

### 3. 上传失败
- 检查USB连接
- 确保设备驱动正确安装
- 尝试按住BOOT按钮重新上传

### 4. 检测不准确
- 确保摄像头镜头清洁
- 调整光照条件
- 保持适当的距离（30-100cm）

## 技术特性

- **处理器**: ESP32-S3 双核处理器
- **摄像头**: OV2640 200万像素
- **AI加速**: ESP32-S3内置AI加速器
- **内存**: 8MB PSRAM
- **连接**: WiFi 802.11 b/g/n
- **接口**: USB-C, Grove接口

## 开发说明

项目结构：
```
src/
├── main.cpp                 # 主程序文件
├── camera_init.cpp         # 摄像头初始化
├── camera_init.h           # 摄像头头文件
├── camera_pins.h           # 引脚定义
├── wifi_serial_bridge.cpp  # WiFi串口桥接实现
└── wifi_serial_bridge.h    # WiFi串口桥接头文件
```

主要功能模块：
1. **摄像头模块** - 负责图像采集和处理
2. **AI检测模块** - 人脸检测和情绪识别
3. **Web服务器** - 提供视频流和控制界面
4. **WiFi模块** - 网络连接和通信
5. **串口桥接** - 远程调试功能

## 更新日志

- **v1.0** - 基础人脸检测和情绪识别功能
- **v1.1** - 添加WiFi串口桥接功能
- **v1.2** - 优化检测算法和Web界面
