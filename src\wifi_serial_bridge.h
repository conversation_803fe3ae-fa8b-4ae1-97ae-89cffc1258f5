#ifndef WIFI_SERIAL_BRIDGE_H
#define WIFI_SERIAL_BRIDGE_H

#include <Arduino.h>
#include <WiFi.h>
#include <WiFiClient.h>
#include <WiFiServer.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

// WiFi串口桥接配置
#define SERIAL_BRIDGE_PORT 8888
#define SERIAL_BUFFER_SIZE 1024
#define WIFI_SERIAL_QUEUE_SIZE 50
#define MAX_CLIENTS 2
#define RECONNECT_DELAY_MS 5000
#define HEARTBEAT_INTERVAL_MS 10000

// 数据包类型
enum PacketType {
    PACKET_SERIAL_DATA = 0x01,
    PACKET_HEARTBEAT = 0x02,
    PACKET_ACK = 0x03,
    PACKET_STATUS = 0x04
};

// 数据包结构
struct SerialPacket {
    uint8_t type;           // 数据包类型
    uint16_t length;        // 数据长度
    uint32_t timestamp;     // 时间戳
    uint8_t data[SERIAL_BUFFER_SIZE]; // 数据内容
    uint16_t checksum;      // 校验和
};

// 连接状态
enum ConnectionStatus {
    STATUS_DISCONNECTED = 0,
    STATUS_CONNECTING = 1,
    STATUS_CONNECTED = 2,
    STATUS_ERROR = 3
};

// WiFi串口桥接类
class WiFiSerialBridge {
private:
    // 网络配置
    WiFiServer* server;
    WiFiClient clients[MAX_CLIENTS];
    int client_count;

    // 客户端模式配置
    WiFiClient client;
    String target_ip;
    uint16_t target_port;

    // 任务句柄
    TaskHandle_t server_task_handle;
    TaskHandle_t client_task_handle;
    TaskHandle_t serial_task_handle;

    // 队列
    QueueHandle_t serial_tx_queue;
    QueueHandle_t serial_rx_queue;

    // 状态
    ConnectionStatus server_status;
    ConnectionStatus client_status;
    bool is_server_mode;
    bool is_client_mode;

    // 统计信息
    unsigned long bytes_sent;
    unsigned long bytes_received;
    unsigned long packets_sent;
    unsigned long packets_received;
    unsigned long last_heartbeat;

    // 内部方法
    uint16_t calculateChecksum(const uint8_t* data, size_t length);
    bool validatePacket(const SerialPacket* packet);
    void sendPacket(WiFiClient& client, PacketType type, const uint8_t* data, size_t length);
    bool receivePacket(WiFiClient& client, SerialPacket* packet);
    void handleClientConnection(WiFiClient& client);
    void processSerialData();
    void sendHeartbeat();

    // 静态任务函数
    static void serverTask(void* parameter);
    static void clientTask(void* parameter);
    static void serialTask(void* parameter);

public:
    WiFiSerialBridge();
    ~WiFiSerialBridge();

    // 初始化和配置
    bool begin();
    void end();

    // 服务器模式
    bool startServer(uint16_t port = SERIAL_BRIDGE_PORT);
    void stopServer();

    // 客户端模式
    bool startClient(const String& ip, uint16_t port = SERIAL_BRIDGE_PORT);
    void stopClient();

    // 双向模式
    bool startBidirectional(const String& target_ip, uint16_t port = SERIAL_BRIDGE_PORT);

    // 数据发送
    size_t write(const uint8_t* data, size_t length);
    size_t write(const String& data);
    size_t println(const String& data);
    size_t printf(const char* format, ...);

    // 数据接收
    int available();
    int read();
    String readString();
    String readStringUntil(char terminator);

    // 状态查询
    bool isServerConnected();
    bool isClientConnected();
    ConnectionStatus getServerStatus();
    ConnectionStatus getClientStatus();

    // 统计信息
    unsigned long getBytesSent();
    unsigned long getBytesReceived();
    unsigned long getPacketsSent();
    unsigned long getPacketsReceived();
    void resetStatistics();

    // 调试和监控
    void printStatus();
    void setDebugMode(bool enabled);
};

// 全局实例
extern WiFiSerialBridge wifiSerial;

#endif // WIFI_SERIAL_BRIDGE_H
