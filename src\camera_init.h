/*
 * SPDX-FileCopyrightText: 2024 M5Stack Technology CO LTD
 *
 * SPDX-License-Identifier: MIT
 */
#pragma once

#include "esp_camera.h"
#include "camera_pins.h"

/**
 * @brief 初始化摄像头
 * 
 * @param xclk_freq_hz 时钟频率 (Hz)
 * @param pixel_format 像素格式 (PIXFORMAT_JPEG, PIXFORMAT_RGB565等)
 * @param frame_size 帧大小 (FRAMESIZE_QVGA, FRAMESIZE_VGA等)
 * @param jpeg_quality JPEG质量 (0-63, 数值越小质量越高)
 * @param fb_count 帧缓冲区数量
 * @return esp_err_t 初始化结果
 */
esp_err_t camera_init(int xclk_freq_hz, pixformat_t pixel_format, framesize_t frame_size, 
                     int jpeg_quality, uint8_t fb_count);

/**
 * @brief 启用摄像头电源
 */
void enable_camera_power();

/**
 * @brief 获取摄像头帧
 * @return camera_fb_t* 摄像头帧缓冲区指针
 */
camera_fb_t* camera_get_frame();

/**
 * @brief 释放摄像头帧
 * @param fb 要释放的帧缓冲区
 */
void camera_return_frame(camera_fb_t* fb);
