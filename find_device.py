#!/usr/bin/env python3
"""
ESP32设备IP地址扫描工具
用于查找网络中的ESP32设备
"""

import socket
import threading
import time
from concurrent.futures import ThreadPoolExecutor
import requests

def check_port(ip, port, timeout=1):
    """检查指定IP和端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((ip, port))
        sock.close()
        return result == 0
    except:
        return False

def check_web_server(ip, timeout=2):
    """检查是否是ESP32 Web服务器"""
    try:
        response = requests.get(f"http://{ip}", timeout=timeout)
        content = response.text.lower()
        # 检查是否包含ESP32相关关键词
        esp32_keywords = ['esp32', 'camera', 'stream', 'atoms3r']
        return any(keyword in content for keyword in esp32_keywords)
    except:
        return False

def scan_ip(base_ip, host_num):
    """扫描单个IP地址"""
    ip = f"{base_ip}.{host_num}"
    
    # 检查常见的ESP32端口
    ports_to_check = [80, 8080, 8888, 81]
    open_ports = []
    
    for port in ports_to_check:
        if check_port(ip, port):
            open_ports.append(port)
    
    if open_ports:
        # 检查是否是Web服务器
        is_web_server = check_web_server(ip)
        return {
            'ip': ip,
            'open_ports': open_ports,
            'is_esp32': is_web_server
        }
    
    return None

def get_local_network():
    """获取本地网络地址段"""
    try:
        # 连接到外部地址来获取本地IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        
        # 提取网络段 (例如: ************* -> 192.168.1)
        return '.'.join(local_ip.split('.')[:-1])
    except:
        return "192.168.1"  # 默认网络段

def main():
    print("🔍 正在扫描网络中的ESP32设备...")
    print("=" * 50)
    
    base_ip = get_local_network()
    print(f"扫描网络段: {base_ip}.1-254")
    print()
    
    found_devices = []
    
    # 使用线程池并发扫描
    with ThreadPoolExecutor(max_workers=50) as executor:
        futures = []
        
        # 扫描1-254的IP地址
        for i in range(1, 255):
            future = executor.submit(scan_ip, base_ip, i)
            futures.append(future)
        
        # 收集结果
        for i, future in enumerate(futures):
            try:
                result = future.result(timeout=3)
                if result:
                    found_devices.append(result)
                    
                    # 实时显示找到的设备
                    ip = result['ip']
                    ports = ', '.join(map(str, result['open_ports']))
                    esp32_status = "✅ 可能是ESP32" if result['is_esp32'] else "❓ 未知设备"
                    
                    print(f"📱 找到设备: {ip}")
                    print(f"   开放端口: {ports}")
                    print(f"   状态: {esp32_status}")
                    print()
                
                # 显示进度
                if (i + 1) % 50 == 0:
                    print(f"进度: {i + 1}/254")
                    
            except Exception as e:
                continue
    
    print("=" * 50)
    print("🎯 扫描完成!")
    
    if found_devices:
        print(f"\n找到 {len(found_devices)} 个设备:")
        print()
        
        for device in found_devices:
            ip = device['ip']
            ports = ', '.join(map(str, device['open_ports']))
            
            print(f"🌐 设备IP: {ip}")
            print(f"   端口: {ports}")
            
            if device['is_esp32']:
                print(f"   🎥 摄像头地址: http://{ip}")
                if 8888 in device['open_ports']:
                    print(f"   🔧 串口桥接: {ip}:8888")
            
            print()
    else:
        print("\n❌ 未找到任何设备")
        print("\n可能的原因:")
        print("1. ESP32设备未连接到WiFi")
        print("2. 设备在不同的网络段")
        print("3. 防火墙阻止了连接")
        print("\n建议:")
        print("1. 检查串口输出确认WiFi连接状态")
        print("2. 确认WiFi网络名称和密码正确")
        print("3. 尝试重启ESP32设备")

if __name__ == "__main__":
    main()
