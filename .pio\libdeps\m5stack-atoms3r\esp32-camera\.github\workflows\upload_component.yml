name: Push component to https://components.espressif.com
on:
  push:
    tags:
      - v*
jobs:
  upload_components:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@master
        with:
          submodules: "recursive"
      - name: Upload component to the component registry
        uses: espressif/github-actions/upload_components@master
        with:
          name: "esp32-camera"
          namespace: "espressif"
          version: ${{ github.ref_name }}
          api_token: ${{ secrets.IDF_COMPONENT_API_TOKEN }}
