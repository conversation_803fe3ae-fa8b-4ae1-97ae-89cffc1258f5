/*
 * SPDX-FileCopyrightText: 2024 M5Stack Technology CO LTD
 *
 * SPDX-License-Identifier: MIT
 */

#include "camera_init.h"
#include "camera_pins.h"
#include <Arduino.h>

static const char* TAG = "camera_init";
static bool camera_initialized = false;

void enable_camera_power() {
    // 配置摄像头电源引脚
    pinMode(CAMERA_POWER_PIN, OUTPUT);
    digitalWrite(CAMERA_POWER_PIN, LOW);  // 启用摄像头电源
    
    Serial.println("Camera power enabled");
    delay(200);  // 等待电源稳定
}

esp_err_t camera_init(int xclk_freq_hz, pixformat_t pixel_format, framesize_t frame_size,
                     int jpeg_quality, uint8_t fb_count) {

    if (camera_initialized) {
        Serial.println("Camera already initialized");
        return ESP_OK;
    }

    Serial.println("Setting up camera configuration...");

    // 摄像头配置结构体
    camera_config_t config;
    memset(&config, 0, sizeof(camera_config_t));  // 清零配置结构体

    config.ledc_channel = LEDC_CHANNEL_0;
    config.ledc_timer = LEDC_TIMER_0;
    config.pin_d0 = CAMERA_PIN_D0;
    config.pin_d1 = CAMERA_PIN_D1;
    config.pin_d2 = CAMERA_PIN_D2;
    config.pin_d3 = CAMERA_PIN_D3;
    config.pin_d4 = CAMERA_PIN_D4;
    config.pin_d5 = CAMERA_PIN_D5;
    config.pin_d6 = CAMERA_PIN_D6;
    config.pin_d7 = CAMERA_PIN_D7;
    config.pin_xclk = CAMERA_PIN_XCLK;
    config.pin_pclk = CAMERA_PIN_PCLK;
    config.pin_vsync = CAMERA_PIN_VSYNC;
    config.pin_href = CAMERA_PIN_HREF;
    config.pin_sccb_sda = CAMERA_PIN_SIOD;
    config.pin_sccb_scl = CAMERA_PIN_SIOC;
    config.pin_pwdn = CAMERA_PIN_PWDN;
    config.pin_reset = CAMERA_PIN_RESET;
    config.xclk_freq_hz = xclk_freq_hz;
    config.pixel_format = pixel_format;
    config.frame_size = frame_size;
    config.jpeg_quality = jpeg_quality;
    config.fb_count = fb_count;
    config.grab_mode = CAMERA_GRAB_WHEN_EMPTY;
    config.fb_location = CAMERA_FB_IN_DRAM;  // 改为使用DRAM而不是PSRAM

    Serial.printf("Camera config: XCLK=%d, Format=%d, Size=%d, Quality=%d, FB=%d\n",
                  xclk_freq_hz, pixel_format, frame_size, jpeg_quality, fb_count);

    // 初始化摄像头
    esp_err_t err = esp_camera_init(&config);
    if (err != ESP_OK) {
        Serial.printf("Camera init failed with error 0x%x\n", err);
        return err;
    }

    // 获取传感器对象并进行配置
    sensor_t* s = esp_camera_sensor_get();
    if (s != NULL) {
        // 垂直翻转
        s->set_vflip(s, 1);
        
        // 根据传感器类型进行特定配置
        if (s->id.PID == OV3660_PID) {
            s->set_brightness(s, 1);   // 增加亮度
            s->set_saturation(s, -2);  // 降低饱和度
        } else if (s->id.PID == OV2640_PID) {
            s->set_vflip(s, 1);
        } else if (s->id.PID == GC0308_PID) {
            s->set_hmirror(s, 0);
        } else if (s->id.PID == GC032A_PID) {
            s->set_vflip(s, 1);
        }
        
        Serial.printf("Camera sensor PID: 0x%x\n", s->id.PID);
    }

    camera_initialized = true;
    Serial.println("Camera initialized successfully");
    return ESP_OK;
}

camera_fb_t* camera_get_frame() {
    if (!camera_initialized) {
        Serial.println("Camera not initialized");
        return NULL;
    }
    return esp_camera_fb_get();
}

void camera_return_frame(camera_fb_t* fb) {
    if (fb != NULL) {
        esp_camera_fb_return(fb);
    }
}
