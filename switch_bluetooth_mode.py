#!/usr/bin/env python3
"""
ESP32-S3 蓝牙模式切换脚本
用于快速切换PICO发送端和DevKitC接收端配置
"""

import os
import shutil
import sys
import argparse

def backup_current_main():
    """备份当前的main.cpp文件"""
    if os.path.exists("src/main.cpp"):
        backup_name = "src/main_backup.cpp"
        counter = 1
        while os.path.exists(backup_name):
            backup_name = f"src/main_backup_{counter}.cpp"
            counter += 1
        
        shutil.copy2("src/main.cpp", backup_name)
        print(f"✅ 已备份当前main.cpp为: {backup_name}")
        return backup_name
    return None

def switch_to_sender():
    """切换到蓝牙发送端模式（PICO）"""
    print("🔄 切换到蓝牙发送端模式（ESP32-S3 PICO）...")
    
    if not os.path.exists("src/bluetooth_sender_pico.cpp"):
        print("❌ 错误: 找不到bluetooth_sender_pico.cpp文件")
        return False
    
    backup_current_main()
    shutil.copy2("src/bluetooth_sender_pico.cpp", "src/main.cpp")
    print("✅ 已切换到蓝牙发送端模式")
    print("📝 使用以下命令编译和上传:")
    print("   pio run -e esp32s3-pico-bluetooth --target upload")
    print("   pio device monitor -e esp32s3-pico-bluetooth")
    return True

def switch_to_receiver():
    """切换到蓝牙接收端模式（DevKitC）"""
    print("🔄 切换到蓝牙接收端模式（ESP32-S3 DevKitC）...")
    
    if not os.path.exists("src/bluetooth_receiver_devkitc.cpp"):
        print("❌ 错误: 找不到bluetooth_receiver_devkitc.cpp文件")
        return False
    
    backup_current_main()
    shutil.copy2("src/bluetooth_receiver_devkitc.cpp", "src/main.cpp")
    print("✅ 已切换到蓝牙接收端模式")
    print("📝 使用以下命令编译和上传:")
    print("   pio run -e esp32s3-devkitc-bluetooth --target upload")
    print("   pio device monitor -e esp32s3-devkitc-bluetooth")
    return True

def restore_backup(backup_file):
    """恢复备份的main.cpp文件"""
    if not os.path.exists(backup_file):
        print(f"❌ 错误: 备份文件 {backup_file} 不存在")
        return False
    
    shutil.copy2(backup_file, "src/main.cpp")
    print(f"✅ 已恢复备份文件: {backup_file}")
    return True

def list_backups():
    """列出所有备份文件"""
    backups = []
    if os.path.exists("src"):
        for file in os.listdir("src"):
            if file.startswith("main_backup") and file.endswith(".cpp"):
                backups.append(file)
    
    if backups:
        print("📁 可用的备份文件:")
        for i, backup in enumerate(backups, 1):
            print(f"   {i}. {backup}")
    else:
        print("📁 没有找到备份文件")
    
    return backups

def show_current_mode():
    """显示当前模式"""
    if not os.path.exists("src/main.cpp"):
        print("❌ 找不到main.cpp文件")
        return
    
    with open("src/main.cpp", "r", encoding="utf-8") as f:
        content = f.read()
    
    if "ESP32-S3 PICO 蓝牙发送端" in content:
        print("📡 当前模式: 蓝牙发送端（ESP32-S3 PICO）")
    elif "ESP32-S3 DevKitC 蓝牙接收端" in content:
        print("📡 当前模式: 蓝牙接收端（ESP32-S3 DevKitC）")
    elif "ATOMS3R 实时摄像头检测系统" in content:
        print("📡 当前模式: 摄像头系统")
    else:
        print("📡 当前模式: 未知")

def main():
    parser = argparse.ArgumentParser(description="ESP32-S3 蓝牙模式切换工具")
    parser.add_argument("mode", nargs="?", choices=["sender", "receiver", "status", "list", "restore"],
                       help="选择模式: sender(发送端), receiver(接收端), status(显示状态), list(列出备份), restore(恢复备份)")
    parser.add_argument("--backup", help="指定要恢复的备份文件名")
    
    args = parser.parse_args()
    
    if not args.mode:
        print("🔧 ESP32-S3 蓝牙模式切换工具")
        print("=" * 40)
        show_current_mode()
        print("\n可用命令:")
        print("  python switch_bluetooth_mode.py sender    - 切换到发送端模式")
        print("  python switch_bluetooth_mode.py receiver  - 切换到接收端模式")
        print("  python switch_bluetooth_mode.py status    - 显示当前模式")
        print("  python switch_bluetooth_mode.py list      - 列出备份文件")
        print("  python switch_bluetooth_mode.py restore --backup <文件名> - 恢复备份")
        return
    
    if args.mode == "sender":
        switch_to_sender()
    elif args.mode == "receiver":
        switch_to_receiver()
    elif args.mode == "status":
        show_current_mode()
    elif args.mode == "list":
        list_backups()
    elif args.mode == "restore":
        if args.backup:
            restore_backup(f"src/{args.backup}")
        else:
            backups = list_backups()
            if backups:
                try:
                    choice = int(input("请选择要恢复的备份文件编号: ")) - 1
                    if 0 <= choice < len(backups):
                        restore_backup(f"src/{backups[choice]}")
                    else:
                        print("❌ 无效的选择")
                except ValueError:
                    print("❌ 请输入有效的数字")

if __name__ == "__main__":
    main()
