#include "SPIFFSEditor.h"
#include <FS.h>

// array size is 4120
#define edit_htm_gz_len 4120
static const byte edit_htm_gz[] PROGMEM  = {
  0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x03, 0xb5, 0x3a, 0x0b, 0x5b, 0xda, 0xc8, 
  0xda, 0x7f, 0x85, 0xa6, 0x67, 0xdd, 0xe4, 0x10, 0x02, 0xa8, 0x75, 0xdb, 0x60, 0x74, 0x41, 0x6d, 
  0x6d, 0xbd, 0x0b, 0xb4, 0xb5, 0x9e, 0x7e, 0xfb, 0x0c, 0xc9, 0x00, 0xa3, 0xc9, 0x4c, 0x9a, 0x0c, 
  0x82, 0x65, 0xf9, 0xef, 0xe7, 0x7d, 0x27, 0x09, 0x49, 0x10, 0xdb, 0xdd, 0xef, 0x39, 0x5b, 0xdb, 
  0x32, 0x33, 0xef, 0x5c, 0xde, 0xfb, 0x0d, 0x77, 0x5f, 0x78, 0xc2, 0x95, 0x8f, 0x21, 0x1d, 0xcb, 
  0xc0, 0xdf, 0xdb, 0xc5, 0xff, 0x2b, 0x3e, 0xe1, 0x23, 0x87, 0xf2, 0xbd, 0x5d, 0xc9, 0xa4, 0x4f, 
  0xf7, 0x8e, 0xba, 0x97, 0x95, 0x23, 0x8f, 0x49, 0x11, 0xed, 0xd6, 0x93, 0x95, 0xdd, 0x58, 0x3e, 
  0xfa, 0xb4, 0x12, 0x50, 0x8f, 0x11, 0x27, 0x76, 0x23, 0x0a, 0x9b, 0x2d, 0x37, 0x98, 0x7f, 0xaf, 
  0x31, 0xee, 0xd1, 0x99, 0xbd, 0xd5, 0x68, 0xb4, 0x42, 0x11, 0x33, 0xc9, 0x04, 0xb7, 0xc9, 0x20, 
  0x16, 0xfe, 0x44, 0xd2, 0x96, 0x4f, 0x87, 0xd2, 0x7e, 0x15, 0xce, 0x5a, 0x03, 0x11, 0x79, 0x34, 
  0xb2, 0x9b, 0xe1, 0xac, 0x02, 0x20, 0xe6, 0x55, 0x5e, 0x6e, 0x6f, 0x6f, 0xb7, 0x06, 0xc4, 0xbd, 
  0x1f, 0x45, 0x62, 0xc2, 0xbd, 0x9a, 0x2b, 0x7c, 0x11, 0xd9, 0x2f, 0x87, 0xaf, 0xf0, 0xa7, 0xe5, 
  0xb1, 0x38, 0xf4, 0xc9, 0xa3, 0xcd, 0x05, 0xa7, 0x70, 0x76, 0x56, 0x8b, 0xc7, 0xc4, 0x13, 0x53, 
  0xbb, 0x51, 0x69, 0x54, 0x9a, 0x0d, 0xb8, 0x24, 0x1a, 0x0d, 0x88, 0xde, 0x30, 0xf1, 0xc7, 0xda, 
  0x36, 0x5a, 0x43, 0xc1, 0x65, 0x2d, 0x66, 0xdf, 0xa9, 0xdd, 0xdc, 0x84, 0xd7, 0xd4, 0x74, 0x48, 
  0x02, 0xe6, 0x3f, 0xda, 0x31, 0xe1, 0x71, 0x2d, 0xa6, 0x11, 0x1b, 0x26, 0xcb, 0x53, 0xca, 0x46, 
  0x63, 0x69, 0xff, 0xd6, 0x68, 0x2c, 0x00, 0xff, 0xca, 0xc4, 0x9f, 0xfb, 0x2c, 0x86, 0xc3, 0x48, 
  0x5e, 0xf2, 0x9e, 0x14, 0xa1, 0xdd, 0x48, 0x50, 0x6f, 0xb4, 0x02, 0x12, 0x8d, 0x18, 0x87, 0x41, 
  0x48, 0x3c, 0x8f, 0xf1, 0x91, 0x9d, 0x1c, 0xf3, 0xd9, 0x7c, 0x49, 0x6d, 0x44, 0x7d, 0x22, 0xd9, 
  0x03, 0x6d, 0x05, 0x8c, 0xd7, 0xa6, 0xcc, 0x93, 0x63, 0x7b, 0x07, 0x70, 0x6c, 0xb9, 0x93, 0x28, 
  0x06, 0x9a, 0x42, 0xc1, 0xb8, 0xa4, 0x91, 0x3a, 0x16, 0x87, 0x84, 0xcf, 0x53, 0x52, 0x91, 0x01, 
  0x19, 0x9d, 0x8c, 0xfb, 0x8c, 0xd3, 0xda, 0xc0, 0x17, 0xee, 0xfd, 0xf2, 0xa5, 0x9d, 0x70, 0x96, 
  0xbe, 0x65, 0x8f, 0xc5, 0x03, 0x8d, 0xe6, 0x39, 0xbb, 0xd4, 0xe9, 0x12, 0xb0, 0x74, 0x35, 0xa5, 
  0x74, 0x61, 0xc9, 0x87, 0x09, 0x40, 0x4d, 0xf5, 0x09, 0x54, 0x2e, 0xf1, 0xcf, 0x49, 0x5a, 0x21, 
  0x3c, 0x39, 0xc2, 0x78, 0x38, 0x91, 0xf3, 0xa7, 0xa2, 0x14, 0x21, 0x71, 0x99, 0x7c, 0x44, 0xfa, 
  0x61, 0xdb, 0x1c, 0x79, 0x09, 0xa7, 0xa2, 0x80, 0xf8, 0x15, 0xe4, 0x79, 0xe5, 0x23, 0x8d, 0x3c, 
  0xc2, 0x89, 0xd9, 0x8e, 0x18, 0xf1, 0xcd, 0x6e, 0xce, 0xf5, 0x5a, 0x20, 0xbe, 0xd7, 0x26, 0x30, 
  0x86, 0xb9, 0x4f, 0x5d, 0x99, 0x30, 0x19, 0xe4, 0x30, 0xb8, 0x67, 0xf2, 0x29, 0xe0, 0xc9, 0x42, 
  0x81, 0x5d, 0x8a, 0x49, 0xe3, 0x44, 0x80, 0x4d, 0xc5, 0x1e, 0xc4, 0x58, 0x51, 0x9e, 0x10, 0x55, 
  0x1b, 0x08, 0x29, 0x45, 0xa0, 0x54, 0x6e, 0x49, 0x70, 0x45, 0x69, 0xcd, 0xeb, 0x27, 0x12, 0x59, 
  0xcf, 0xfd, 0xc2, 0xf5, 0x2d, 0xe0, 0xab, 0x64, 0x2e, 0xf1, 0x6b, 0xc4, 0x67, 0x23, 0x6e, 0x07, 
  0xcc, 0xf3, 0x7c, 0x5a, 0xd0, 0x5a, 0x7b, 0x12, 0xf9, 0xba, 0x47, 0x24, 0xb1, 0x59, 0x40, 0x46, 
  0xb4, 0x1e, 0xf2, 0x11, 0x40, 0x63, 0xba, 0xb3, 0x6d, 0xb2, 0x8f, 0x9d, 0x8b, 0xeb, 0x69, 0xe3, 
  0xe4, 0xdd, 0x48, 0xb4, 0xe1, 0xcf, 0x79, 0xb7, 0x3f, 0x3e, 0xea, 0x8f, 0x60, 0xd4, 0xc1, 0x69, 
  0xfb, 0xea, 0xa0, 0x7d, 0x05, 0x1f, 0x07, 0xcd, 0xea, 0xdd, 0xf0, 0x1b, 0x2e, 0x74, 0xde, 0x79, 
  0x9d, 0x5e, 0xff, 0xa8, 0xdd, 0x3e, 0xa9, 0xbf, 0x3f, 0x9f, 0x7e, 0x3a, 0xd9, 0x41, 0x70, 0xc7, 
  0x6f, 0x5c, 0x7f, 0x1c, 0x37, 0xfa, 0x9b, 0x6f, 0x02, 0xef, 0xd8, 0x1b, 0xbb, 0x41, 0xbf, 0x7d, 
  0xf5, 0xe9, 0xfa, 0xe1, 0x26, 0xe8, 0x8f, 0xba, 0x9f, 0x9a, 0xe3, 0x2f, 0x9b, 0x1f, 0xbb, 0x5f, 
  0x3e, 0xbd, 0xbd, 0xa7, 0x9f, 0x8f, 0x3f, 0x7c, 0xe9, 0x4d, 0xe1, 0xc0, 0xa1, 0xe8, 0xf6, 0xaf, 
  0x3b, 0x1f, 0x3b, 0xa3, 0x2f, 0x1d, 0xf7, 0x68, 0x36, 0xf0, 0xcf, 0x3b, 0xef, 0xda, 0x83, 0xf6, 
  0xa6, 0x4b, 0xe9, 0xa8, 0x77, 0xdd, 0x99, 0xbc, 0x3f, 0xb9, 0x18, 0x31, 0x36, 0xee, 0x7e, 0x39, 
  0xef, 0xb9, 0x07, 0xaf, 0x4e, 0xfb, 0xc7, 0x6d, 0x36, 0x3e, 0xff, 0x70, 0xdd, 0xb8, 0x7f, 0x77, 
  0x72, 0x70, 0xe8, 0xde, 0x7c, 0xb8, 0xd9, 0x39, 0xdc, 0xaa, 0xff, 0xf6, 0xdb, 0x99, 0x77, 0xc1, 
  0x78, 0xef, 0xe1, 0x7b, 0x7b, 0x74, 0x30, 0x7d, 0xfd, 0x18, 0xf7, 0xc6, 0xef, 0x1f, 0x78, 0xfd, 
  0xa3, 0xb8, 0x7b, 0xff, 0x78, 0x06, 0xff, 0x2e, 0x2f, 0xab, 0x83, 0x6e, 0x33, 0xee, 0x5f, 0xbd, 
  0xff, 0xb8, 0x19, 0xbf, 0x79, 0x15, 0x76, 0x0e, 0x0f, 0x1f, 0x82, 0xc1, 0x65, 0x3d, 0xf0, 0xee, 
  0x87, 0xf2, 0xf5, 0x96, 0x0c, 0x6f, 0x46, 0x93, 0x2f, 0xdf, 0x5e, 0x7d, 0x18, 0xd7, 0x2f, 0x28, 
  0xb9, 0x19, 0x57, 0x1f, 0xbf, 0x3f, 0xbe, 0x1e, 0xf7, 0x8e, 0x1f, 0xce, 0x7d, 0x32, 0x3b, 0x3f, 
  0x77, 0xbf, 0x07, 0x55, 0x9f, 0xbc, 0xb9, 0xe8, 0xf9, 0x24, 0x6a, 0xf6, 0xbd, 0x76, 0xbd, 0x7a, 
  0xb0, 0xd9, 0xde, 0x96, 0xd1, 0xf5, 0x01, 0x3f, 0xdc, 0xba, 0xeb, 0xbe, 0xee, 0x74, 0x9a, 0x62, 
  0xf0, 0x6d, 0xf3, 0xdd, 0xfd, 0xce, 0xbb, 0xfe, 0xce, 0xd5, 0xe0, 0xaa, 0xdd, 0xdd, 0xee, 0xf4, 
  0xc9, 0x4d, 0xf7, 0xaa, 0x3d, 0xdc, 0x1e, 0x8c, 0xc7, 0x27, 0x27, 0xbd, 0xb7, 0x5e, 0xfb, 0x7b, 
  0xd4, 0xbe, 0x98, 0xb6, 0x67, 0x47, 0xfd, 0xf6, 0x71, 0xf5, 0xe4, 0xe8, 0xac, 0xd1, 0xec, 0xde, 
  0x6c, 0x8d, 0xce, 0x76, 0xa6, 0x9d, 0xf8, 0xa8, 0x7d, 0xd5, 0x69, 0x8c, 0x3e, 0x54, 0x03, 0xf2, 
  0x45, 0x1c, 0x6c, 0x8d, 0xde, 0xef, 0xb0, 0xcb, 0x1b, 0xd2, 0x7e, 0xdf, 0xf9, 0x10, 0xb3, 0xeb, 
  0xe0, 0xb8, 0xdf, 0x68, 0xb7, 0x4f, 0x2f, 0xe8, 0xdb, 0x83, 0x2d, 0x72, 0xb2, 0xe9, 0x7e, 0x02, 
  0xfe, 0xf7, 0x3f, 0xd3, 0xdf, 0xaa, 0xed, 0xe9, 0x45, 0xc3, 0x77, 0xdf, 0xd0, 0xde, 0xf1, 0x4d, 
  0x4f, 0x49, 0xe7, 0xc8, 0x7f, 0xdb, 0xbb, 0xef, 0x4e, 0xae, 0x82, 0x83, 0x03, 0xa3, 0xc2, 0x45, 
  0x2d, 0xa2, 0x21, 0x25, 0xb2, 0xe8, 0xae, 0x96, 0xf6, 0x01, 0xba, 0x95, 0x6b, 0x61, 0x6a, 0xa7, 
  0x92, 0xce, 0x64, 0xcd, 0xa3, 0xae, 0x88, 0x88, 0xda, 0x03, 0x07, 0x68, 0x84, 0x2a, 0xb6, 0xf8, 
  0x5d, 0xf9, 0xcf, 0x4a, 0xe2, 0x3f, 0x2b, 0x84, 0x7b, 0x15, 0x3d, 0x33, 0x04, 0x74, 0x1e, 0x1e, 
  0x7d, 0x60, 0x2e, 0xad, 0x85, 0x6c, 0x46, 0xfd, 0x9a, 0x3a, 0x6c, 0x37, 0x8c, 0xb9, 0xb2, 0xb7, 
  0x6c, 0x1b, 0xe1, 0xa0, 0x6a, 0xea, 0xd6, 0x6c, 0xc1, 0xbb, 0x23, 0x2e, 0x05, 0xc7, 0x06, 0x26, 
  0x13, 0xe0, 0x67, 0x62, 0x3b, 0x22, 0xaa, 0x0d, 0x26, 0xa3, 0x21, 0x9b, 0x81, 0x41, 0x0f, 0x19, 
  0x67, 0x92, 0x56, 0x9a, 0xf1, 0xe2, 0xf7, 0xec, 0x9a, 0x7b, 0xfa, 0x38, 0x8c, 0x48, 0x40, 0xe3, 
  0xca, 0x5f, 0xbc, 0x66, 0x3e, 0x8c, 0x44, 0x90, 0x3b, 0x91, 0x85, 0x14, 0x85, 0xc9, 0x62, 0xf1, 
  0x72, 0x12, 0xfa, 0x82, 0x00, 0x99, 0x6b, 0x3c, 0x47, 0xe2, 0x4f, 0x23, 0x65, 0x56, 0x4b, 0xbf, 
  0x9a, 0x9a, 0xd9, 0x26, 0x9a, 0x65, 0xd1, 0xac, 0x37, 0xb7, 0x73, 0xd3, 0xad, 0xa9, 0xbd, 0xe8, 
  0xef, 0xd7, 0x44, 0x0a, 0xf4, 0x07, 0x05, 0x77, 0xf7, 0x52, 0x02, 0x4b, 0x9f, 0x79, 0x5c, 0x3d, 
  0x92, 0x7a, 0x86, 0x25, 0x02, 0x89, 0xa3, 0x6e, 0x2a, 0x4f, 0x9d, 0x51, 0x02, 0xfb, 0x16, 0x2f, 
  0xa9, 0x8a, 0x7b, 0xe6, 0xcb, 0x30, 0x02, 0x69, 0xd0, 0xe9, 0x8f, 0xee, 0xcc, 0x68, 0x2a, 0xdf, 
  0x9d, 0xdc, 0x99, 0x44, 0xbc, 0x94, 0x84, 0x3c, 0xec, 0x29, 0x5c, 0xb3, 0xab, 0x9f, 0x52, 0x05, 
  0xe0, 0x25, 0x36, 0xaf, 0x10, 0x9b, 0x1f, 0x72, 0x75, 0x6b, 0xe7, 0x97, 0x14, 0x87, 0xed, 0xc6, 
  0x2f, 0x0b, 0x2b, 0xdd, 0x9b, 0x85, 0xe4, 0x66, 0x03, 0xfe, 0x64, 0x91, 0xf7, 0x75, 0x8e, 0xc2, 
  0xe0, 0x15, 0xfe, 0x64, 0x08, 0xe2, 0x3d, 0x05, 0xe0, 0xd6, 0xf6, 0x9b, 0xd7, 0xde, 0x20, 0x03, 
  0xa6, 0x84, 0x3d, 0x0b, 0x8f, 0x88, 0xc7, 0x26, 0xb1, 0xfd, 0xaa, 0xf1, 0x4b, 0xca, 0xcf, 0xcd, 
  0x6d, 0xa4, 0x7d, 0x29, 0x4b, 0x9c, 0xe4, 0x1a, 0x1b, 0x87, 0x8c, 0x57, 0x36, 0xe3, 0x0a, 0xca, 
  0x9b, 0x44, 0x4b, 0xc5, 0x2c, 0x45, 0xfb, 0xc5, 0xef, 0xb9, 0x6a, 0xe2, 0xfe, 0x79, 0xe3, 0x97, 
  0xb9, 0x8c, 0x20, 0x9a, 0x0c, 0x21, 0xd6, 0xd8, 0x91, 0x90, 0x44, 0x52, 0xbd, 0x61, 0x2c, 0x80, 
  0xb8, 0x35, 0x80, 0xad, 0x9d, 0x86, 0x47, 0x47, 0xc6, 0x62, 0xb1, 0x5b, 0x57, 0x71, 0x0d, 0xd2, 
  0x16, 0x37, 0x62, 0xa1, 0xdc, 0x1b, 0x4e, 0xb8, 0x8b, 0x38, 0x54, 0x46, 0x54, 0xa7, 0xc6, 0x3c, 
  0xa2, 0x72, 0x12, 0xf1, 0x0a, 0xe4, 0x41, 0x13, 0x54, 0x76, 0x6b, 0x44, 0xe5, 0x51, 0xa2, 0xf7, 
  0x9d, 0xc7, 0xf7, 0x1e, 0xec, 0x58, 0x2c, 0x0f, 0xb8, 0x6b, 0x0f, 0x80, 0x01, 0xc3, 0x7b, 0xe9, 
  0x99, 0xd2, 0x7e, 0x08, 0x30, 0x70, 0xc9, 0x09, 0x7d, 0xd4, 0xa9, 0xc9, 0x96, 0x07, 0xa9, 0x85, 
  0xeb, 0x7a, 0xb6, 0x0b, 0x60, 0xd2, 0x98, 0x3f, 0x00, 0x13, 0xb8, 0x43, 0x6f, 0xd9, 0x57, 0x93, 
  0x38, 0x12, 0x3e, 0x5a, 0xe9, 0x6e, 0xbe, 0x4b, 0xf6, 0x6b, 0x4d, 0x9b, 0xec, 0xf2, 0xfd, 0x26, 
  0x98, 0x97, 0xb1, 0xd0, 0xd0, 0x85, 0x00, 0xb7, 0xa8, 0xa7, 0x39, 0x0e, 0xa6, 0x6e, 0x62, 0x58, 
  0xf9, 0x7c, 0x76, 0x7a, 0x2c, 0x65, 0x78, 0x4d, 0xbf, 0x4d, 0x68, 0x2c, 0x37, 0x36, 0xf4, 0xf2, 
  0x82, 0xb3, 0x7c, 0xcb, 0x00, 0x3e, 0x3d, 0x66, 0x88, 0x70, 0x3a, 0xad, 0xb4, 0x5d, 0x4c, 0x57, 
  0x3e, 0x5f, 0x0c, 0xee, 0xc0, 0xc2, 0x75, 0xed, 0x2c, 0x9e, 0x05, 0xfe, 0xa6, 0x85, 0xc7, 0x7b, 
  0xbd, 0x4b, 0x6b, 0xc7, 0x6a, 0x68, 0xc6, 0xc2, 0x25, 0xd2, 0x1d, 0x23, 0xe5, 0x8b, 0xbf, 0x75, 
  0x78, 0xeb, 0xef, 0x1d, 0x66, 0x6e, 0x24, 0x62, 0x31, 0x94, 0xd9, 0xf9, 0xf2, 0xd9, 0x71, 0x24, 
  0xa6, 0xea, 0xd8, 0x51, 0x14, 0x89, 0x48, 0xd7, 0x7a, 0x63, 0x16, 0x57, 0x06, 0xb0, 0x08, 0x29, 
  0x02, 0x88, 0x02, 0x34, 0x84, 0x0b, 0x59, 0x89, 0x27, 0x61, 0x08, 0xbc, 0x5d, 0xe1, 0x87, 0x05, 
  0x57, 0x19, 0x2d, 0xe4, 0xf0, 0xd5, 0x84, 0x4e, 0xa8, 0x97, 0x2e, 0xd3, 0xa8, 0xc4, 0x17, 0xb8, 
  0xd0, 0xfa, 0x86, 0x70, 0xe7, 0xf6, 0xab, 0xa9, 0x66, 0xd1, 0x84, 0x73, 0x30, 0x3e, 0xe7, 0x45, 
  0x33, 0x99, 0x03, 0x75, 0x63, 0xb8, 0xd5, 0xe1, 0x13, 0xdf, 0x5f, 0xb4, 0x56, 0xee, 0xb2, 0x42, 
  0xd0, 0x3b, 0x81, 0xf2, 0x70, 0xe6, 0x7f, 0x44, 0xc9, 0xaa, 0x9d, 0xcb, 0xd8, 0x98, 0xb3, 0xa1, 
  0x5e, 0xbe, 0xb5, 0x61, 0xbe, 0xd0, 0x5f, 0x50, 0x50, 0xfd, 0x58, 0x12, 0xee, 0xa2, 0x18, 0x13, 
  0x5e, 0x18, 0x89, 0x36, 0x48, 0x07, 0xb7, 0xb7, 0x40, 0x49, 0xb5, 0xc4, 0x98, 0x35, 0xc3, 0x52, 
  0x8a, 0x6c, 0xa5, 0x36, 0xe2, 0x68, 0x2a, 0x4b, 0xd1, 0x14, 0x65, 0xdc, 0x24, 0x26, 0x73, 0x34, 
  0xad, 0x05, 0xcf, 0x50, 0x2b, 0x24, 0x60, 0x35, 0x71, 0xf1, 0xea, 0xb7, 0x60, 0x19, 0x87, 0x90, 
  0x9d, 0x18, 0xcc, 0xc9, 0xc0, 0x2d, 0xea, 0xc7, 0xf0, 0xfc, 0xfa, 0xfd, 0x29, 0x2a, 0x60, 0x50, 
  0x3a, 0x5e, 0x2f, 0x00, 0x56, 0xc9, 0xf6, 0x19, 0xac, 0x0a, 0x2f, 0x39, 0x8e, 0xc3, 0xf6, 0xb5, 
  0x77, 0x47, 0x3d, 0x1c, 0x51, 0x2b, 0xa0, 0x72, 0x2c, 0xbc, 0x7d, 0x6d, 0x5f, 0xb3, 0x35, 0xf8, 
  0xbb, 0xa1, 0x99, 0xb0, 0x8b, 0x72, 0x57, 0x78, 0xb4, 0x7f, 0xfd, 0xfe, 0x40, 0x04, 0x21, 0x58, 
  0x34, 0xd8, 0x87, 0x30, 0xaa, 0x9a, 0xa3, 0x55, 0xd7, 0x40, 0xb2, 0xeb, 0x6f, 0xc5, 0x57, 0xa3, 
  0x55, 0x66, 0x38, 0xc8, 0xbd, 0x2c, 0xd2, 0x92, 0x40, 0x2c, 0xc1, 0xc1, 0x04, 0xbd, 0xc7, 0x18, 
  0xed, 0xde, 0x1d, 0x43, 0xf9, 0x42, 0x1d, 0x9d, 0x3b, 0xc5, 0x2d, 0x60, 0x54, 0xd4, 0x2c, 0x48, 
  0x7b, 0xdb, 0x71, 0xb8, 0xa5, 0x0e, 0x75, 0xf1, 0x10, 0x58, 0xcc, 0x8f, 0xf8, 0x8c, 0xce, 0x48, 
  0x33, 0x89, 0x05, 0xe9, 0x9f, 0x8f, 0x7e, 0x5a, 0xe7, 0x16, 0xbe, 0x35, 0x89, 0x4d, 0xbc, 0x24, 
  0x06, 0xfc, 0x63, 0xda, 0x83, 0x40, 0x6f, 0x98, 0x0d, 0xe0, 0x85, 0x4c, 0xd4, 0xc8, 0xf2, 0x29, 
  0x1f, 0xc9, 0x31, 0x5c, 0x2d, 0x0b, 0x9a, 0x64, 0x98, 0xcb, 0xd9, 0xc6, 0x86, 0xb4, 0x32, 0x4d, 
  0xd1, 0xb3, 0x43, 0xf1, 0x98, 0x0d, 0xa5, 0x6e, 0x18, 0xa0, 0xb1, 0xe6, 0x13, 0xe6, 0xea, 0x65, 
  0xaa, 0x43, 0x0a, 0x8a, 0x95, 0xc2, 0x4c, 0x6a, 0x41, 0x02, 0x5a, 0x65, 0xe6, 0x8b, 0x86, 0x51, 
  0x66, 0x4e, 0x4c, 0xb9, 0x07, 0x17, 0xda, 0x3f, 0x3d, 0xac, 0x8e, 0xb2, 0xa2, 0x12, 0x74, 0x65, 
  0x94, 0xe0, 0x59, 0xbe, 0x4f, 0xa6, 0x42, 0x38, 0xa6, 0xc8, 0x2e, 0x5d, 0x3b, 0x80, 0xfc, 0x1f, 
  0xd3, 0x04, 0x54, 0x7e, 0xcd, 0xd4, 0x48, 0x18, 0xfa, 0x90, 0x29, 0x23, 0xa7, 0xeb, 0xb3, 0xda, 
  0x74, 0x3a, 0xad, 0xa1, 0x5b, 0xae, 0xc1, 0x0b, 0x89, 0xd0, 0x3d, 0x6d, 0x1d, 0x86, 0x0c, 0x48, 
  0x5e, 0x98, 0x31, 0x86, 0xa0, 0x55, 0xab, 0x5c, 0xf2, 0x6b, 0xc5, 0x80, 0x9a, 0xe5, 0x7b, 0x56, 
  0xf0, 0xcc, 0xa5, 0xbb, 0xbb, 0xbd, 0x02, 0x22, 0x03, 0x74, 0xc0, 0xc6, 0xc2, 0x84, 0xe8, 0x6a, 
  0x17, 0xfd, 0xb0, 0x09, 0x66, 0x54, 0x74, 0x05, 0x56, 0x38, 0x89, 0xc7, 0xfa, 0x1c, 0x50, 0xb7, 
  0xa5, 0x99, 0xf0, 0xca, 0xa6, 0x66, 0xa2, 0xa6, 0x36, 0x37, 0x33, 0x7d, 0xb0, 0xc9, 0xc2, 0x28, 
  0xb9, 0x8c, 0x3f, 0xff, 0x54, 0xb3, 0x5c, 0xba, 0xf9, 0x8d, 0x99, 0x80, 0x17, 0x0b, 0x65, 0xb7, 
  0xe9, 0x8e, 0x58, 0xe9, 0xf8, 0x8a, 0x4b, 0x69, 0xe5, 0x81, 0x47, 0x85, 0x98, 0xb7, 0xcc, 0xa7, 
  0xfd, 0x34, 0xb7, 0x02, 0x6c, 0x79, 0x16, 0x37, 0x88, 0x03, 0x81, 0x49, 0x1b, 0x4c, 0x20, 0x3e, 
  0x73, 0xcd, 0x68, 0x11, 0x8b, 0x71, 0x4e, 0xa3, 0xe3, 0xde, 0xd9, 0xa9, 0xa3, 0x5d, 0xd3, 0x21, 
  0x68, 0xe8, 0xb8, 0x72, 0x0a, 0xf5, 0x9d, 0x66, 0xaa, 0x88, 0x67, 0x81, 0x80, 0x80, 0xe3, 0x07, 
  0x63, 0xe6, 0x7b, 0x3a, 0x49, 0x1c, 0x23, 0x53, 0x57, 0xa8, 0x7a, 0x0f, 0x6e, 0x60, 0x96, 0xf2, 
  0x63, 0xda, 0x10, 0x1e, 0x04, 0x3b, 0xb6, 0x82, 0x89, 0x2f, 0x59, 0xe8, 0x53, 0xf4, 0x84, 0xcc, 
  0xe2, 0x10, 0x90, 0x1d, 0x0d, 0x4b, 0x1d, 0x84, 0x31, 0xcf, 0xd1, 0x92, 0x84, 0x2f, 0x4d, 0x13, 
  0xd7, 0xbd, 0xc2, 0x92, 0x57, 0x44, 0xe9, 0x15, 0x51, 0x3c, 0x1b, 0x12, 0x39, 0xd6, 0x4c, 0x91, 
  0x3e, 0x8c, 0x59, 0x33, 0xce, 0x92, 0xa7, 0x32, 0x18, 0x84, 0x3d, 0x02, 0x98, 0x7c, 0x24, 0x3e, 
  0x38, 0x69, 0xad, 0xbe, 0xee, 0x21, 0x91, 0x3c, 0xe4, 0x96, 0x39, 0xe2, 0x16, 0x39, 0x92, 0x70, 
  0x70, 0xdd, 0x61, 0x37, 0x39, 0x1c, 0x97, 0x0f, 0xc7, 0xc5, 0xc3, 0x07, 0x4a, 0x10, 0xeb, 0x0e, 
  0xc7, 0xc9, 0x61, 0xaf, 0x44, 0xa2, 0xa7, 0x48, 0x4c, 0xb2, 0xc8, 0x1a, 0x72, 0x13, 0x09, 0xd2, 
  0x4c, 0xaf, 0x44, 0xa6, 0x87, 0x8e, 0x86, 0x0c, 0x7c, 0xea, 0x61, 0x4c, 0xf0, 0x2c, 0xec, 0x5d, 
  0x38, 0x9b, 0x8d, 0x35, 0x6f, 0x78, 0xc9, 0x1b, 0x7e, 0x19, 0xc1, 0xa5, 0x96, 0x44, 0x49, 0x2a, 
  0xb1, 0xd9, 0x68, 0xbc, 0x70, 0xe8, 0x3e, 0xf1, 0xa1, 0x62, 0xd5, 0xb5, 0xa3, 0xeb, 0xeb, 0x8b, 
  0xeb, 0x5b, 0x70, 0xb8, 0x55, 0xed, 0xab, 0x5d, 0xd1, 0xaa, 0xd2, 0xb0, 0xd1, 0x69, 0x29, 0xad, 
  0xb8, 0x04, 0xc6, 0xea, 0xc2, 0x7a, 0x40, 0x86, 0x1a, 0x0b, 0xbf, 0x48, 0x68, 0xa5, 0x4b, 0x1e, 
  0x68, 0x65, 0x1d, 0xa1, 0xbe, 0x61, 0xc6, 0xe0, 0x71, 0x5d, 0xb0, 0xf1, 0x7b, 0xa7, 0x18, 0xe0, 
  0x5e, 0x14, 0x27, 0x49, 0x04, 0x43, 0xa5, 0xce, 0x22, 0x4f, 0x4b, 0xa6, 0xd7, 0xe8, 0xa9, 0x40, 
  0xa9, 0x61, 0x66, 0xda, 0x6f, 0x81, 0x21, 0xea, 0xda, 0x65, 0xbf, 0x07, 0xee, 0xa3, 0x8e, 0xec, 
  0xd2, 0xc0, 0x16, 0x23, 0x63, 0xb1, 0x44, 0x0e, 0xbc, 0x26, 0x4a, 0xad, 0x0f, 0x35, 0xf6, 0x72, 
  0x29, 0x1d, 0xa0, 0x26, 0x2c, 0x4c, 0x7f, 0x2d, 0x4a, 0xd2, 0xa2, 0x33, 0xea, 0x42, 0x88, 0x09, 
  0x08, 0xbe, 0x1b, 0x03, 0x4d, 0xe9, 0x44, 0x43, 0xeb, 0x5f, 0x7b, 0xe6, 0x19, 0xee, 0x98, 0xee, 
  0xda, 0xdd, 0x10, 0x3e, 0x81, 0xdb, 0x0e, 0xb3, 0x50, 0xba, 0x71, 0xea, 0xf3, 0x7f, 0x42, 0x7e, 
  0x66, 0x3a, 0xea, 0xc8, 0x6d, 0xe3, 0xab, 0xb9, 0x24, 0x69, 0x85, 0x1f, 0x17, 0xdd, 0x15, 0x86, 
  0xa0, 0x38, 0x4a, 0x16, 0x63, 0xe4, 0x3c, 0x28, 0xc2, 0x52, 0x4b, 0x5c, 0x42, 0x35, 0x70, 0xad, 
  0x0c, 0xf1, 0x4f, 0x82, 0xe3, 0x5f, 0x26, 0x20, 0xc7, 0x51, 0x99, 0xa2, 0xc9, 0x9d, 0xba, 0xbe, 
  0x6f, 0xff, 0xc7, 0xd2, 0x6f, 0xff, 0xcf, 0xfa, 0x5a, 0x35, 0x8c, 0xfd, 0x7f, 0xd5, 0x15, 0x87, 
  0x75, 0x69, 0xdc, 0x36, 0x31, 0x73, 0xad, 0xeb, 0xd6, 0xbf, 0x8d, 0xff, 0x58, 0x0a, 0x5c, 0x82, 
  0xb5, 0x1e, 0x04, 0x94, 0x0c, 0xf8, 0x52, 0x9a, 0xb2, 0x12, 0x74, 0xe7, 0x0e, 0x29, 0x89, 0xb1, 
  0x2a, 0xab, 0x9a, 0xa5, 0x55, 0x39, 0x14, 0x91, 0x2b, 0xae, 0xaf, 0x07, 0x15, 0x1d, 0x68, 0xb7, 
  0x9b, 0x21, 0x86, 0xb4, 0xa6, 0x95, 0x13, 0x44, 0x93, 0xc4, 0x62, 0x3d, 0xf6, 0x50, 0xb4, 0x06, 
  0x4c, 0xdd, 0xe7, 0xb8, 0x6f, 0xd5, 0xfc, 0x32, 0xae, 0x50, 0x33, 0x87, 0x3e, 0x17, 0xf3, 0xe5, 
  0xfa, 0x94, 0x0b, 0xd6, 0x73, 0x5b, 0xf9, 0x75, 0x97, 0x05, 0xa3, 0x4a, 0x1c, 0xb9, 0x4e, 0x22, 
  0xaa, 0x7d, 0xfc, 0xcf, 0xf9, 0x15, 0x2d, 0x6e, 0xe3, 0x0f, 0x77, 0x00, 0xc9, 0x0e, 0xc8, 0x9f, 
  0x5a, 0x5c, 0x4c, 0x75, 0xa3, 0xfa, 0xab, 0x56, 0x51, 0x37, 0x3a, 0x5a, 0x40, 0x66, 0x69, 0x87, 
  0x10, 0xeb, 0x97, 0x56, 0x05, 0xe7, 0x59, 0xc3, 0x29, 0x5d, 0x50, 0xbd, 0x39, 0x32, 0x91, 0xa2, 
  0x55, 0xc9, 0xea, 0xa2, 0xa4, 0x31, 0xa5, 0x55, 0xea, 0x7b, 0xbf, 0x2e, 0x0a, 0x86, 0x0f, 0x91, 
  0x2b, 0x61, 0x0d, 0x55, 0xac, 0x98, 0xf8, 0xc0, 0x09, 0x59, 0x32, 0x5b, 0x9a, 0xf8, 0x8e, 0x24, 
  0x56, 0xf8, 0x0c, 0xe0, 0x74, 0x25, 0x10, 0x98, 0xa1, 0xce, 0x8d, 0x7d, 0xbd, 0x14, 0x3e, 0x76, 
  0xb1, 0x95, 0xb1, 0x77, 0x99, 0x70, 0x1a, 0x0a, 0x29, 0x9c, 0x69, 0xcf, 0x18, 0x8e, 0x07, 0xc7, 
  0xcd, 0xc6, 0xee, 0xb2, 0x2e, 0x1a, 0x08, 0xef, 0xb1, 0x50, 0x4d, 0xc5, 0x9d, 0xc7, 0x03, 0x9f, 
  0xc4, 0xf1, 0x39, 0xc8, 0x40, 0xd7, 0xdc, 0x00, 0x58, 0x9e, 0x65, 0x48, 0xe5, 0x23, 0x11, 0x0d, 
  0x04, 0xd8, 0xa9, 0xc2, 0x4a, 0x42, 0x1e, 0x64, 0x4f, 0xe0, 0x62, 0xd0, 0x96, 0x35, 0x88, 0x61, 
  0x5f, 0xfa, 0x27, 0x58, 0xb9, 0x4b, 0xc7, 0xf1, 0xcf, 0x60, 0x57, 0x08, 0x9f, 0x6b, 0xb8, 0xca, 
  0x0c, 0x0c, 0x91, 0xab, 0x68, 0x1f, 0x8a, 0x29, 0x47, 0xac, 0x96, 0xa8, 0xb3, 0x9f, 0x3b, 0x54, 
  0xd4, 0x55, 0x2f, 0x3d, 0x57, 0x53, 0x45, 0x31, 0xea, 0x6c, 0xae, 0x74, 0x19, 0x0c, 0x73, 0xeb, 
  0xc5, 0x3f, 0x43, 0x6b, 0x21, 0x86, 0xaf, 0x21, 0x55, 0xa0, 0x29, 0x3f, 0x25, 0x15, 0x3c, 0x11, 
  0x64, 0x60, 0x19, 0xa1, 0xe2, 0x9f, 0x89, 0x1c, 0x87, 0x47, 0xa7, 0x47, 0xbd, 0xa3, 0xa2, 0xaf, 
  0x2c, 0x57, 0xd7, 0x3f, 0x0d, 0x89, 0x7a, 0x5c, 0x22, 0x36, 0xb6, 0x5c, 0xfc, 0x3c, 0x87, 0x6c, 
  0x15, 0x9d, 0x9f, 0x61, 0x06, 0x7a, 0x0c, 0x97, 0x6b, 0x98, 0x96, 0xff, 0x43, 0xcc, 0xcd, 0xad, 
  0xd9, 0x4f, 0x32, 0xd1, 0x62, 0x6e, 0xa7, 0xd8, 0x4d, 0x30, 0x99, 0x90, 0x05, 0x7d, 0x43, 0x9e, 
  0xc2, 0x7a, 0x5a, 0x53, 0x17, 0xf5, 0x4c, 0x82, 0x31, 0xac, 0xa8, 0xe1, 0x7a, 0xeb, 0x98, 0xa0, 
  0x55, 0x41, 0x3a, 0x22, 0x4e, 0xc5, 0x94, 0x46, 0x07, 0x24, 0xa6, 0x90, 0x9a, 0xee, 0xe7, 0x36, 
  0x83, 0x50, 0xc3, 0x0e, 0xd7, 0xed, 0x02, 0x7a, 0x12, 0x70, 0x1a, 0x48, 0xb1, 0x08, 0x98, 0x49, 
  0xa0, 0x6f, 0x52, 0x7a, 0x01, 0x72, 0x68, 0xf0, 0x1e, 0x40, 0xf5, 0x61, 0x92, 0xbd, 0xe9, 0x06, 
  0x54, 0x1b, 0x98, 0xe6, 0x5f, 0x46, 0x22, 0x24, 0x23, 0x92, 0x24, 0xfa, 0x66, 0x39, 0x0b, 0x2f, 
  0xd2, 0xae, 0xdc, 0x3a, 0xd4, 0xb6, 0x65, 0xee, 0xc5, 0x6e, 0x24, 0x7c, 0xbf, 0x27, 0xc2, 0xfd, 
  0x67, 0xd6, 0xed, 0xe5, 0x7a, 0x36, 0x48, 0x05, 0x94, 0x6f, 0x31, 0xc5, 0xda, 0x4b, 0x4f, 0xe9, 
  0x50, 0xee, 0x3f, 0x07, 0xf8, 0xd9, 0xb5, 0xb8, 0xc7, 0x74, 0xa1, 0x64, 0x03, 0x5e, 0xc3, 0xea, 
  0xe7, 0xaa, 0x80, 0xe8, 0x94, 0xcd, 0x6e, 0xaa, 0x0c, 0xa4, 0xe8, 0x66, 0xea, 0xe1, 0xa0, 0x7a, 
  0x00, 0xf3, 0xd6, 0x87, 0x98, 0x6c, 0x1d, 0xbb, 0x86, 0x8e, 0x5b, 0xd5, 0xc2, 0x59, 0xbe, 0x06, 
  0xec, 0x73, 0xe2, 0x64, 0x29, 0xd2, 0x09, 0x68, 0xb8, 0x59, 0xc6, 0x77, 0x4d, 0x86, 0xef, 0x39, 
  0x20, 0xa5, 0xe1, 0x10, 0xaa, 0xb7, 0x4f, 0x18, 0x74, 0x4c, 0x7f, 0x39, 0x3f, 0x56, 0x41, 0xa7, 
  0x85, 0x42, 0x0c, 0xc4, 0x24, 0xa6, 0x62, 0x22, 0x4b, 0x12, 0xd4, 0x97, 0xc4, 0xec, 0xba, 0x7f, 
  0xfe, 0xb9, 0x9c, 0xec, 0xb9, 0x55, 0x2f, 0x9f, 0xde, 0xec, 0xc6, 0x85, 0xc9, 0x5e, 0x5c, 0xf5, 
  0x41, 0x41, 0xfe, 0xc7, 0x56, 0x42, 0xc0, 0x4a, 0x40, 0x3f, 0x32, 0xa5, 0xcb, 0x2d, 0x66, 0x92, 
  0xfb, 0x8d, 0x67, 0xf2, 0x13, 0xaa, 0x72, 0x10, 0xc8, 0x75, 0x56, 0xd3, 0x10, 0x69, 0xc4, 0x53, 
  0x86, 0x5d, 0x26, 0xf0, 0x12, 0x2e, 0x28, 0xb6, 0x26, 0x21, 0x25, 0xb7, 0xd5, 0x68, 0x2c, 0x83, 
  0x7c, 0xe4, 0xa7, 0xc3, 0xbb, 0x38, 0x1d, 0xb8, 0x71, 0x36, 0x9a, 0x15, 0x80, 0x90, 0x90, 0xa7, 
  0x60, 0xc1, 0x87, 0xe9, 0x90, 0x71, 0x96, 0xdd, 0x93, 0x01, 0xb3, 0xcf, 0x30, 0x4c, 0x47, 0xe1, 
  0x38, 0x1b, 0x8d, 0xe9, 0x6c, 0x79, 0x4e, 0x64, 0x50, 0x8f, 0xa6, 0xa3, 0x29, 0x54, 0x09, 0xe9, 
  0xd0, 0x17, 0x23, 0xcd, 0x4e, 0x6c, 0xff, 0x45, 0x63, 0x91, 0x0e, 0x9a, 0x39, 0x53, 0xc2, 0xff, 
  0x21, 0x53, 0x42, 0x3e, 0xca, 0x48, 0x0c, 0xb3, 0xd1, 0x88, 0x65, 0x04, 0x0e, 0x82, 0xf0, 0x87, 
  0x98, 0x70, 0x9d, 0x9b, 0x79, 0xdf, 0xf4, 0x89, 0x67, 0x76, 0x1c, 0xba, 0xb1, 0xb1, 0x6a, 0xff, 
  0x79, 0xeb, 0x94, 0x9b, 0x5a, 0x92, 0xb6, 0x15, 0xf2, 0x17, 0x95, 0xdf, 0xac, 0xe6, 0x2f, 0xad, 
  0xac, 0x59, 0xc5, 0x1c, 0x9e, 0x6a, 0x13, 0x58, 0x78, 0xa3, 0x25, 0x76, 0x59, 0x4b, 0x54, 0xab, 
  0x46, 0x52, 0xce, 0x3a, 0x8e, 0xc3, 0x6f, 0xc5, 0x57, 0x55, 0x81, 0x6d, 0x6c, 0x94, 0xdd, 0xa4, 
  0xaf, 0x37, 0x4c, 0x05, 0x4c, 0x92, 0x5d, 0x1c, 0x61, 0x31, 0x66, 0xa0, 0xcf, 0x37, 0x1b, 0xe6, 
  0x87, 0xee, 0xc5, 0x39, 0xf6, 0xa8, 0xc0, 0xfd, 0x49, 0xa3, 0xe8, 0xb1, 0x83, 0x84, 0x96, 0x72, 
  0x4c, 0xc2, 0xe6, 0xcd, 0x32, 0x20, 0xa9, 0x2f, 0x96, 0x6d, 0xb9, 0x30, 0xd1, 0xa2, 0x8c, 0x94, 
  0x47, 0x95, 0xb8, 0xe8, 0x0b, 0xe4, 0xc3, 0x64, 0x7d, 0x5d, 0x99, 0x76, 0x16, 0xf2, 0xa2, 0xa4, 
  0x64, 0x9d, 0x7f, 0x31, 0x70, 0x2d, 0x96, 0x23, 0x75, 0xdd, 0x6a, 0x7e, 0x9d, 0x7c, 0xc1, 0x9f, 
  0xb5, 0x40, 0xb0, 0x59, 0xbd, 0xdc, 0x20, 0x72, 0x3d, 0xd2, 0xc0, 0x41, 0x31, 0xae, 0x3d, 0x5f, 
  0x06, 0x3c, 0xab, 0x51, 0x3c, 0xd3, 0x28, 0xfe, 0xd4, 0xcc, 0x96, 0x0a, 0x5f, 0xb0, 0x99, 0x55, 
  0x3d, 0x5f, 0xbe, 0xdd, 0x1a, 0x00, 0xbe, 0xf7, 0xad, 0xdc, 0x40, 0x01, 0xa2, 0xcc, 0xb3, 0x08, 
  0x40, 0x23, 0x85, 0xf5, 0x3b, 0xf2, 0x40, 0x92, 0x9e, 0x7f, 0xf9, 0xd8, 0x3a, 0x2b, 0x84, 0xed, 
  0xee, 0x1f, 0x38, 0x2a, 0xee, 0xcc, 0x6d, 0x3c, 0xce, 0x87, 0x05, 0x53, 0x95, 0xeb, 0x2c, 0x3f, 
  0x77, 0x07, 0xca, 0xf0, 0x21, 0x95, 0xc9, 0xc4, 0x2d, 0x17, 0x09, 0x5b, 0xb0, 0x47, 0xa8, 0xaa, 
  0x1f, 0xad, 0xae, 0xbe, 0x9d, 0xb1, 0xd4, 0x45, 0x86, 0xb9, 0x84, 0x72, 0x80, 0x72, 0x47, 0xa0, 
  0x96, 0xe5, 0x8b, 0x58, 0x30, 0x91, 0xa4, 0x67, 0x10, 0x60, 0x0f, 0xa2, 0x00, 0x62, 0x00, 0x62, 
  0x09, 0x08, 0x8a, 0x28, 0x30, 0x98, 0x84, 0x94, 0xf4, 0xa6, 0x0c, 0x92, 0x30, 0xd0, 0xc8, 0x3a, 
  0x24, 0xc4, 0xa5, 0x16, 0x2a, 0x27, 0x56, 0x06, 0xf9, 0x37, 0x16, 0xc5, 0x7c, 0x09, 0xcc, 0xe3, 
  0xb9, 0x84, 0x69, 0x51, 0x2c, 0xb3, 0xf0, 0x44, 0xa9, 0x20, 0x5b, 0x5f, 0x4b, 0xfd, 0xa8, 0xda, 
  0x4a, 0x63, 0x5e, 0xe2, 0x0d, 0x20, 0xff, 0x80, 0xb0, 0xa4, 0xda, 0x3c, 0xc0, 0x02, 0xbb, 0x30, 
  0xd3, 0x80, 0x6a, 0x17, 0x8c, 0x86, 0x92, 0xa8, 0xab, 0x4a, 0x5c, 0x95, 0x3c, 0xa4, 0xfc, 0x4d, 
  0x35, 0xe4, 0x85, 0xa2, 0xda, 0xc5, 0x60, 0xd3, 0xa5, 0x71, 0xac, 0x76, 0xe0, 0x0d, 0x67, 0x60, 
  0x13, 0xba, 0x06, 0x54, 0xd7, 0x03, 0x18, 0x21, 0x9f, 0xf0, 0x2e, 0x00, 0xf4, 0xc6, 0x34, 0x48, 
  0x21, 0x12, 0x87, 0x00, 0x22, 0x08, 0xfa, 0x97, 0x42, 0xaa, 0xab, 0xc2, 0x3a, 0xf6, 0x1b, 0x9b, 
  0xf5, 0x86, 0xf9, 0xf4, 0xda, 0x7e, 0x4c, 0xbb, 0x62, 0x28, 0x7b, 0x64, 0x10, 0xeb, 0xd8, 0x40, 
  0x7d, 0xba, 0x03, 0x40, 0x5d, 0xf0, 0x22, 0xfa, 0x66, 0xfa, 0xde, 0x31, 0x84, 0x5b, 0x1f, 0x43, 
  0x6e, 0xf2, 0x0d, 0xc8, 0x29, 0xe3, 0x34, 0x3d, 0x09, 0xc0, 0xee, 0x58, 0x4c, 0x2f, 0x23, 0xc6, 
  0xe5, 0x99, 0xaa, 0xfe, 0x74, 0x6c, 0x72, 0x02, 0xc5, 0x49, 0xd3, 0x42, 0x79, 0x96, 0xac, 0x9b, 
  0x31, 0x47, 0x27, 0x65, 0x97, 0x7a, 0x1a, 0xe6, 0x00, 0xf4, 0x09, 0x7c, 0xa6, 0x3d, 0x9f, 0x42, 
  0xe1, 0xa8, 0x1d, 0xc8, 0xc8, 0xaf, 0x75, 0x35, 0x33, 0x20, 0x2e, 0x4c, 0x92, 0x3d, 0x30, 0x5f, 
  0x98, 0x68, 0xab, 0xf6, 0x33, 0x09, 0x78, 0x31, 0xfd, 0x2a, 0x25, 0xe1, 0x64, 0xa5, 0x7f, 0x81, 
  0xc0, 0x8e, 0x2f, 0x06, 0xfa, 0xad, 0xfc, 0x6a, 0xce, 0xd1, 0xce, 0x6d, 0xbe, 0x30, 0xcc, 0x9f, 
  0x74, 0x31, 0x88, 0x19, 0x83, 0x37, 0x95, 0x90, 0x03, 0x8e, 0x32, 0xa1, 0x1a, 0x55, 0x0d, 0x2a, 
  0x20, 0xf0, 0x50, 0xd8, 0xb1, 0xbd, 0xe0, 0xfe, 0xa3, 0x0d, 0xc1, 0xe3, 0x27, 0x54, 0x4f, 0xb8, 
  0x27, 0x7e, 0x44, 0xf5, 0x97, 0x15, 0xaa, 0xbf, 0xac, 0xa3, 0x9a, 0x96, 0x45, 0x05, 0x93, 0x3e, 
  0x5c, 0x7b, 0x46, 0x38, 0x19, 0xd1, 0x08, 0x16, 0xf0, 0x11, 0x14, 0xc0, 0xdf, 0xc2, 0x2c, 0xa2, 
  0x3f, 0xc6, 0xac, 0x8b, 0xfd, 0xdf, 0x27, 0xf8, 0x65, 0xab, 0xff, 0x0f, 0x2c, 0xf1, 0xc1, 0xf5, 
  0x58, 0xa6, 0x49, 0xbc, 0xb3, 0x5a, 0x46, 0x3e, 0xdf, 0x10, 0x51, 0x4e, 0xc7, 0xa1, 0x98, 0x6e, 
  0x17, 0xbc, 0xc9, 0xdf, 0xb7, 0xab, 0xe2, 0x8b, 0x3f, 0x8a, 0x8a, 0xf8, 0x61, 0xd3, 0x85, 0xe9, 
  0xa1, 0x4e, 0x60, 0x8f, 0x2d, 0xf7, 0x2b, 0x82, 0x77, 0x20, 0xff, 0x3b, 0x05, 0x12, 0xf4, 0x4c, 
  0x1d, 0xe7, 0xc0, 0x1d, 0x47, 0x07, 0x56, 0x42, 0xad, 0x0b, 0xc4, 0x25, 0x5f, 0x31, 0x58, 0x63, 
  0x88, 0x8c, 0xc0, 0x04, 0x40, 0x12, 0x32, 0x83, 0xfa, 0xed, 0xfe, 0xc6, 0xd7, 0x2a, 0xc4, 0x25, 
  0x07, 0x3e, 0x0c, 0x07, 0x06, 0x1b, 0x5f, 0xff, 0x6d, 0xd4, 0x47, 0xec, 0x49, 0x81, 0x41, 0x40, 
  0x6d, 0x1d, 0x54, 0xd8, 0x52, 0x00, 0xcc, 0x1c, 0x14, 0x24, 0x99, 0xc8, 0x1e, 0xf8, 0xc0, 0xdf, 
  0x86, 0x83, 0x0f, 0xe5, 0x19, 0xc0, 0x77, 0xae, 0xe9, 0xc4, 0x6b, 0xd9, 0xef, 0x3b, 0x68, 0x66, 
  0xa1, 0x59, 0xa5, 0xe1, 0x2f, 0x21, 0xa8, 0x52, 0x95, 0x16, 0x9d, 0xb8, 0xba, 0x56, 0x35, 0x33, 
  0x70, 0x50, 0x8c, 0x00, 0x1a, 0x16, 0x46, 0x79, 0xdd, 0x85, 0x60, 0x03, 0xbf, 0xbb, 0x4e, 0xbe, 
  0xb4, 0x4e, 0xbf, 0xbc, 0xae, 0xb8, 0x63, 0xcc, 0x3f, 0xa4, 0x33, 0x91, 0xc3, 0xda, 0xeb, 0x0a, 
  0x14, 0x84, 0x40, 0xb5, 0xea, 0x3d, 0xe1, 0x17, 0x1b, 0xb1, 0x5d, 0xaf, 0xbb, 0x1e, 0xbf, 0xc3, 
  0xbc, 0x42, 0x4c, 0xbc, 0xa1, 0x4f, 0x22, 0x8a, 0xba, 0x5a, 0x27, 0x77, 0x64, 0x56, 0xf7, 0xd9, 
  0x20, 0xae, 0xa3, 0x9c, 0x9a, 0xd6, 0xa6, 0xb5, 0x83, 0x23, 0xeb, 0x2e, 0xde, 0x5b, 0x7d, 0x61, 
  0x6f, 0x19, 0xbd, 0x11, 0xdd, 0x34, 0x46, 0x24, 0x12, 0x48, 0x36, 0x38, 0xcf, 0x7c, 0xf1, 0xad, 
  0xa5, 0xa1, 0xd6, 0x68, 0x25, 0x83, 0xb4, 0x39, 0x91, 0x3c, 0xa3, 0x99, 0xe9, 0x22, 0x89, 0x1f, 
  0xb9, 0x8b, 0xdf, 0x23, 0x2c, 0x6f, 0x19, 0x83, 0xd6, 0x96, 0x33, 0x1e, 0xb5, 0xb3, 0x48, 0x3b, 
  0x56, 0x02, 0xa0, 0x10, 0xaa, 0xbd, 0x51, 0xd4, 0x8b, 0xbd, 0x5d, 0x28, 0x0e, 0x91, 0x09, 0x09, 
  0xff, 0x2b, 0x2a, 0x9b, 0x4a, 0x27, 0x40, 0x18, 0x00, 0x97, 0x3b, 0x32, 0x19, 0xad, 0x2c, 0xa3, 
  0x90, 0x56, 0x96, 0x12, 0x0d, 0x58, 0x59, 0x4c, 0x43, 0x5b, 0xda, 0xbb, 0x2b, 0xfe, 0x92, 0x42, 
  0xb6, 0x91, 0xa9, 0x9e, 0x0c, 0xee, 0x2d, 0x77, 0x69, 0xd6, 0x1f, 0x49, 0x76, 0xef, 0xfd, 0x17, 
  0x62, 0xe9, 0xfb, 0x8c, 0x7f, 0x29, 0x00, 0x00
};

#define SPIFFS_MAXLENGTH_FILEPATH 32

/* Exclusion list feature not needed and omitted */

// WEB HANDLER IMPLEMENTATION

SPIFFSEditor::SPIFFSEditor(const fs::FS& fs, const String& username, const String& password)
:_fs(fs)
,_username(username)
,_password(password)
,_authenticated(false)
,_startTime(0)
{}

#ifdef ESP8266
SPIFFSEditor::SPIFFSEditor(const String& username, const String& password, const fs::FS& fs) : SPIFFSEditor(fs, username, password) {};
#endif

bool SPIFFSEditor::canHandle(AsyncWebServerRequest *request){
  if(request->url().equalsIgnoreCase(F("/edit"))){
    if(request->method() == HTTP_GET){
      if(request->hasParam("list"))
        return true;
      if(request->hasParam("edit")){
        if (request->arg("edit").indexOf("wsec") > -1) return false; //make sure wsec.json is not served
        request->_tempFile = _fs.open(request->arg("edit"), "r");
        if(!request->_tempFile){
          return false;
        }
#ifdef ESP32
        if(request->_tempFile.isDirectory()){
          request->_tempFile.close();
          return false;
        }
#endif
      }
      if(request->hasParam(F("download"))){
        if (request->arg(F("download")).indexOf("wsec") > -1) return false; //make sure wsec.json is not served
        request->_tempFile = _fs.open(request->arg(F("download")), "r");
        if(!request->_tempFile){
          return false;
        }
#ifdef ESP32
        if(request->_tempFile.isDirectory()){
          request->_tempFile.close();
          return false;
        }
#endif
      }
      request->addInterestingHeader(F("If-Modified-Since"));
      return true;
    }
    else if(request->method() == HTTP_POST)
      return true;
    else if(request->method() == HTTP_DELETE)
      return true;
    else if(request->method() == HTTP_PUT)
      return true;

  }
  return false;
}


void SPIFFSEditor::handleRequest(AsyncWebServerRequest *request){
  if(_username.length() && _password.length() && !request->authenticate(_username.c_str(), _password.c_str()))
    return request->requestAuthentication();

  if(request->method() == HTTP_GET){
    if(request->hasParam("list")){
      String path = request->getParam("list")->value();
#ifdef ESP32
      File dir = _fs.open(path);
#else
      Dir dir = _fs.openDir(path);
#endif
      path = String();
      String output = "[";
#ifdef ESP32
      File entry = dir.openNextFile();
      while(entry){
#else
      while(dir.next()){
        fs::File entry = dir.openFile("r");
#endif
        String fname = entry.name();
        if (fname.indexOf("wsec") == -1) {
          if (output != "[") output += ',';
          output += F("{\"type\":\"file\",\"name\":\"");
          if (fname[0] != '/') output += '/';
          output += fname;
          output += F("\",\"size\":");
          output += String(entry.size());
          output += '}';
        }
#ifdef ESP32
        entry = dir.openNextFile();
#else
        entry.close();
#endif
      }
#ifdef ESP32
      dir.close();
#endif
      output += ']';
      request->send(200, FPSTR(CONTENT_TYPE_JSON), output);
      output = String();
    }
    else if(request->hasParam("edit") || request->hasParam(F("download"))){
      request->send(request->_tempFile, request->_tempFile.name(), String(), request->hasParam(F("download")));
    }
    else {
      const char * buildTime = __DATE__ " " __TIME__ " GMT";
      if (request->header(F("If-Modified-Since")).equals(buildTime)) {
        request->send(304);
      } else {
        AsyncWebServerResponse *response = request->beginResponse_P(200, FPSTR(CONTENT_TYPE_HTML), edit_htm_gz, edit_htm_gz_len);
        response->addHeader(F("Content-Encoding"), F("gzip"));
        response->addHeader(F("Last-Modified"), buildTime);
        request->send(response);
      }
    }
  } else if(request->method() == HTTP_DELETE){
    if(request->hasParam("path", true)){
        _fs.remove(request->getParam("path", true)->value());
      request->send(200, "", "DELETE: "+request->getParam("path", true)->value());
    } else
      request->send(404);
  } else if(request->method() == HTTP_POST){
    if(request->hasParam("data", true, true) && _fs.exists(request->getParam("data", true, true)->value()))
      request->send(200, "", "UPLOADED: "+request->getParam("data", true, true)->value());
    else
      request->send(500);
  } else if(request->method() == HTTP_PUT){
    if(request->hasParam("path", true)){
      String filename = request->getParam("path", true)->value();
      if(_fs.exists(filename)){
        request->send(200);
      } else {
        fs::File f = _fs.open(filename, "w");
        if(f){
          f.write((uint8_t)0x00);
          f.close();
          request->send(200, "", "CREATE: "+filename);
        } else {
          request->send(500);
        }
      }
    } else
      request->send(400);
  }
}

void SPIFFSEditor::handleUpload(AsyncWebServerRequest *request, const String& filename, size_t index, uint8_t *data, size_t len, bool final){
  if(!index){
    if(!_username.length() || request->authenticate(_username.c_str(),_password.c_str())){
      _authenticated = true;
      request->_tempFile = _fs.open(filename, "w");
      _startTime = millis();
    }
  }
  if(_authenticated && request->_tempFile){
    if(len){
      request->_tempFile.write(data,len);
    }
    if(final){
      request->_tempFile.close();
    }
  }
}
