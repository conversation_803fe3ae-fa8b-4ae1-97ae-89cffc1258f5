# This workflow warns and then closes issues and PRs that have had no activity for a specified amount of time.
#
# You can adjust the behavior by modifying this file.
# For more information, see:
# https://github.com/actions/stale
name: Mark stale issues and pull requests

on:
  schedule:
  - cron: '20 9 * * *'

jobs:
  stale:

    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write

    steps:
    - uses: actions/stale@v3
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: 'This issue appears to be stale. Please close it if its no longer valid.'
        stale-pr-message: 'This pull request appears to be stale. Please close it if its no longer valid.'
        stale-issue-label: 'no-issue-activity'
        stale-pr-label: 'no-pr-activity'
