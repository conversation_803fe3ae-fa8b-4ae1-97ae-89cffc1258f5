# ESP32-S3 蓝牙通信设置指南

## 概述
本项目包含完整的ESP32-S3蓝牙通信解决方案，支持PICO和DevKitC之间的数据传输。

## 硬件要求
- ESP32-S3 PICO-1 (发送端)
- ESP32-S3 DevKitC-1 (接收端)

## 软件配置

### 方法1：使用现有代码文件

#### PICO端设置（发送端）
1. 备份当前main.cpp：
   ```bash
   mv src/main.cpp src/main_backup.cpp
   ```

2. 复制蓝牙发送端代码：
   ```bash
   cp src/bluetooth_sender_pico.cpp src/main.cpp
   ```

3. 编译并上传：
   ```bash
   pio run -e esp32s3-pico-bluetooth --target upload
   ```

4. 监视串口输出：
   ```bash
   pio device monitor -e esp32s3-pico-bluetooth
   ```

#### DevKitC端设置（接收端）
1. 在新的项目副本中，复制蓝牙接收端代码：
   ```bash
   cp src/bluetooth_receiver_devkitc.cpp src/main.cpp
   ```

2. 编译并上传：
   ```bash
   pio run -e esp32s3-devkitc-bluetooth --target upload
   ```

3. 监视串口输出：
   ```bash
   pio device monitor -e esp32s3-devkitc-bluetooth
   ```

### 方法2：修改platformio.ini使用不同源文件

可以修改platformio.ini来指定不同的源文件，而不需要重命名main.cpp。

## 连接流程

1. **启动顺序**：
   - 先启动PICO端（发送端）
   - 等待看到"蓝牙服务已启动，等待连接..."
   - 再启动DevKitC端（接收端）

2. **预期输出**：
   
   **PICO端（发送端）**：
   ```
   ESP32-S3 PICO 蓝牙发送端启动
   初始化蓝牙...
   蓝牙服务已启动，等待连接...
   设备名称: ESP32-S3-PICO-Sender
   设备已连接
   数据已发送: {"device":"ESP32-S3-PICO","timestamp":12345,...}
   ```

   **DevKitC端（接收端）**：
   ```
   ESP32-S3 DevKitC 蓝牙接收端启动
   初始化蓝牙扫描...
   开始扫描BLE设备...
   发现BLE设备: ...
   找到目标设备！
   连接成功，开始接收数据
   接收到数据: {"device":"ESP32-S3-PICO",...}
   --- 解析后的数据 ---
   设备: ESP32-S3-PICO
   温度: 25.67°C
   湿度: 62.34%
   ...
   ```

## 数据格式

传输的JSON数据包含以下字段：
- `device`: 设备名称
- `timestamp`: 时间戳
- `temperature`: 温度（°C）
- `humidity`: 湿度（%）
- `light_level`: 光照强度（lux）
- `motion_detected`: 运动检测（0/1）
- `status`: 状态信息
- `battery_level`: 电池电量（%）

## 故障排除

### 常见问题

1. **连接失败**：
   - 确保两个设备都正确烧录了对应的代码
   - 检查UUID是否匹配
   - 重启两个设备，先启动发送端

2. **数据接收异常**：
   - 检查JSON格式是否正确
   - 查看串口输出的错误信息
   - 确认蓝牙连接状态

3. **编译错误**：
   - 确保安装了所需的库依赖
   - 检查platformio.ini配置
   - 清理并重新编译：`pio run --target clean`

### 调试技巧

1. **增加调试输出**：
   - 在代码中添加更多Serial.println()语句
   - 监控内存使用情况

2. **检查蓝牙状态**：
   - 使用手机蓝牙扫描工具查看设备是否可见
   - 检查设备名称和服务UUID

## 扩展功能

### 添加真实传感器数据
可以将模拟数据替换为真实传感器读取：
- 温湿度传感器（DHT22、SHT30等）
- 光照传感器（BH1750等）
- 运动传感器（PIR等）

### 双向通信
当前实现是单向通信，可以扩展为双向：
- 在接收端添加数据发送功能
- 在发送端添加数据接收处理

### 数据存储
可以添加数据存储功能：
- SD卡存储
- SPIFFS文件系统
- 云端数据上传
