#ifndef _GC032A_SETTINGS_H_
#define _GC032A_SETTINGS_H_

#include <stdint.h>
#include <stdbool.h>
#include "esp_attr.h"
#include "gc032a_regs.h"


#define REG_DLY 0xffff
#define REGLIST_TAIL 0x0000


/*
 * The default register settings, as obtained from OmniVision.  There
 * is really no making sense of most of these - lots of "reserved" values
 * and such.
 *
 */
static const uint16_t gc032a_default_regs[][2] = {
    /*System*/
    {0xf3, 0xff},
    {0xf5, 0x06},
    {0xf7, 0x01},
    {0xf8, 0x03},
    {0xf9, 0xce},
    {0xfa, 0x00},
    {0xfc, 0x02},
    {0xfe, 0x02},
    {0x81, 0x03},

    {0xfe, 0x00},
    {0x77, 0x64},
    {0x78, 0x40},
    {0x79, 0x60},
    /*ANALOG & CISCTL*/
    {0xfe, 0x00},
    {0x03, 0x01},
    {0x04, 0xce},
    {0x05, 0x01},
    {0x06, 0xad},
    {0x07, 0x00},
    {0x08, 0x10},
    {0x0a, 0x00},
    {0x0c, 0x00},
    {0x0d, 0x01},
    {0x0e, 0xe8}, // height 488
    {0x0f, 0x02},
    {0x10, 0x88}, // width 648
    {0x17, 0x54},
    {0x19, 0x08},
    {0x1a, 0x0a},
    {0x1f, 0x40},
    {0x20, 0x30},
    {0x2e, 0x80},
    {0x2f, 0x2b},
    {0x30, 0x1a},
    {0xfe, 0x02},
    {0x03, 0x02},
    {0x05, 0xd7},
    {0x06, 0x60},
    {0x08, 0x80},
    {0x12, 0x89},

    /*blk*/
    {0xfe, 0x00},
    {0x18, 0x02},
    {0xfe, 0x02},
    {0x40, 0x22},
    {0x45, 0x00},
    {0x46, 0x00},
    {0x49, 0x20},
    {0x4b, 0x3c},
    {0x50, 0x20},
    {0x42, 0x10},

    /*isp*/
    {0xfe, 0x01},
    {0x0a, 0xc5},
    {0x45, 0x00},
    {0xfe, 0x00},
    {0x40, 0xff},
    {0x41, 0x25},
    {0x42, 0xcf},
    {0x43, 0x10},
    {0x44, 0x83},
    {0x46, 0x23},
    {0x49, 0x03},
    {0x52, 0x02},
    {0x54, 0x00},
    {0xfe, 0x02},
    {0x22, 0xf6},

    /*Shading*/
    {0xfe, 0x01},
    {0xc1, 0x38},
    {0xc2, 0x4c},
    {0xc3, 0x00},
    {0xc4, 0x32},
    {0xc5, 0x24},
    {0xc6, 0x16},
    {0xc7, 0x08},
    {0xc8, 0x08},
    {0xc9, 0x00},
    {0xca, 0x20},
    {0xdc, 0x8a},
    {0xdd, 0xa0},
    {0xde, 0xa6},
    {0xdf, 0x75},

    /*AWB*/
    {0xfe, 0x01},
    {0x7c, 0x09},
    {0x65, 0x06},
    {0x7c, 0x08},
    {0x56, 0xf4},
    {0x66, 0x0f},
    {0x67, 0x84},
    {0x6b, 0x80},
    {0x6d, 0x12},
    {0x6e, 0xb0},
    {0x86, 0x00},
    {0x87, 0x00},
    {0x88, 0x00},
    {0x89, 0x00},
    {0x8a, 0x00},
    {0x8b, 0x00},
    {0x8c, 0x00},
    {0x8d, 0x00},
    {0x8e, 0x00},
    {0x8f, 0x00},
    {0x90, 0x00},
    {0x91, 0x00},
    {0x92, 0xf4},
    {0x93, 0xd5},
    {0x94, 0x50},
    {0x95, 0x0f},
    {0x96, 0xf4},
    {0x97, 0x2d},
    {0x98, 0x0f},
    {0x99, 0xa6},
    {0x9a, 0x2d},
    {0x9b, 0x0f},
    {0x9c, 0x59},
    {0x9d, 0x2d},
    {0x9e, 0xaa},
    {0x9f, 0x67},
    {0xa0, 0x59},
    {0xa1, 0x00},
    {0xa2, 0x00},
    {0xa3, 0x0a},
    {0xa4, 0x00},
    {0xa5, 0x00},
    {0xa6, 0xd4},
    {0xa7, 0x9f},
    {0xa8, 0x55},
    {0xa9, 0xd4},
    {0xaa, 0x9f},
    {0xab, 0xac},
    {0xac, 0x9f},
    {0xad, 0x55},
    {0xae, 0xd4},
    {0xaf, 0xac},
    {0xb0, 0xd4},
    {0xb1, 0xa3},
    {0xb2, 0x55},
    {0xb3, 0xd4},
    {0xb4, 0xac},
    {0xb5, 0x00},
    {0xb6, 0x00},
    {0xb7, 0x05},
    {0xb8, 0xd6},
    {0xb9, 0x8c},

    /*CC*/
    {0xfe, 0x01},
    {0xd0, 0x40},
    {0xd1, 0xf8},
    {0xd2, 0x00},
    {0xd3, 0xfa},
    {0xd4, 0x45},
    {0xd5, 0x02},

    {0xd6, 0x30},
    {0xd7, 0xfa},
    {0xd8, 0x08},
    {0xd9, 0x08},
    {0xda, 0x58},
    {0xdb, 0x02},
    {0xfe, 0x00},

    /*Gamma*/
    {0xfe, 0x00},
    {0xba, 0x00},
    {0xbb, 0x04},
    {0xbc, 0x0a},
    {0xbd, 0x0e},
    {0xbe, 0x22},
    {0xbf, 0x30},
    {0xc0, 0x3d},
    {0xc1, 0x4a},
    {0xc2, 0x5d},
    {0xc3, 0x6b},
    {0xc4, 0x7a},
    {0xc5, 0x85},
    {0xc6, 0x90},
    {0xc7, 0xa5},
    {0xc8, 0xb5},
    {0xc9, 0xc2},
    {0xca, 0xcc},
    {0xcb, 0xd5},
    {0xcc, 0xde},
    {0xcd, 0xea},
    {0xce, 0xf5},
    {0xcf, 0xff},

    /*Auto Gamma*/
    {0xfe, 0x00},
    {0x5a, 0x08},
    {0x5b, 0x0f},
    {0x5c, 0x15},
    {0x5d, 0x1c},
    {0x5e, 0x28},
    {0x5f, 0x36},
    {0x60, 0x45},
    {0x61, 0x51},
    {0x62, 0x6a},
    {0x63, 0x7d},
    {0x64, 0x8d},
    {0x65, 0x98},
    {0x66, 0xa2},
    {0x67, 0xb5},
    {0x68, 0xc3},
    {0x69, 0xcd},
    {0x6a, 0xd4},
    {0x6b, 0xdc},
    {0x6c, 0xe3},
    {0x6d, 0xf0},
    {0x6e, 0xf9},
    {0x6f, 0xff},

    /*Gain*/
    {0xfe, 0x00},
    {0x70, 0x50},

    /*AEC*/
    {0xfe, 0x00},
    {0x4f, 0x01},
    {0xfe, 0x01},
    {0x0d, 0x00},
    {0x12, 0xa0},
    {0x13, 0x3a},
    {0x44, 0x04},
    {0x1f, 0x30},
    {0x20, 0x40},
    {0x26, 0x9a},
    {0x3e, 0x20},
    {0x3f, 0x2d},
    {0x40, 0x40},
    {0x41, 0x5b},
    {0x42, 0x82},
    {0x43, 0xb7},
    {0x04, 0x0a},
    {0x02, 0x79},
    {0x03, 0xc0},

    /*measure window*/
    {0xfe, 0x01},
    {0xcc, 0x08},
    {0xcd, 0x08},
    {0xce, 0xa4},
    {0xcf, 0xec},

    /*DNDD*/
    {0xfe, 0x00},
    {0x81, 0xb8},
    {0x82, 0x12},
    {0x83, 0x0a},
    {0x84, 0x01},
    {0x86, 0x50},
    {0x87, 0x18},
    {0x88, 0x10},
    {0x89, 0x70},
    {0x8a, 0x20},
    {0x8b, 0x10},
    {0x8c, 0x08},
    {0x8d, 0x0a},

    /*Intpee*/
    {0xfe, 0x00},
    {0x8f, 0xaa},
    {0x90, 0x9c},
    {0x91, 0x52},
    {0x92, 0x03},
    {0x93, 0x03},
    {0x94, 0x08},
    {0x95, 0x44},
    {0x97, 0x00},
    {0x98, 0x00},

    /*ASDE*/
    {0xfe, 0x00},
    {0xa1, 0x30},
    {0xa2, 0x41},
    {0xa4, 0x30},
    {0xa5, 0x20},
    {0xaa, 0x30},
    {0xac, 0x32},

    /*YCP*/
    {0xfe, 0x00},
    {0xd1, 0x3c},
    {0xd2, 0x3c},
    {0xd3, 0x38},
    {0xd6, 0xf4},
    {0xd7, 0x1d},
    {0xdd, 0x73},
    {0xde, 0x84},

    /*Banding*/
    {0xfe, 0x00},
    {0x05, 0x01},
    {0x06, 0xad},
    {0x07, 0x00},
    {0x08, 0x10},

    {0xfe, 0x01},
    {0x25, 0x00},
    {0x26, 0x9a},

    {0x27, 0x01},
    {0x28, 0xce},
    {0x29, 0x02},
    {0x2a, 0x68},
    {0x2b, 0x02},
    {0x2c, 0x68},
    {0x2d, 0x07},
    {0x2e, 0xd2},
    {0x2f, 0x0b},
    {0x30, 0x6e},
    {0x31, 0x0e},
    {0x32, 0x70},
    {0x33, 0x12},
    {0x34, 0x0c},
    {0x3c, 0x30},

    /*Analog&Cisctl*/
    {0xfe, 0x00},
    {0x05, 0x01},
    {0x06, 0xa0},
    {0x07, 0x00},
    {0x08, 0x20},
    {0x0a, 0x78},
    {0x0c, 0xa0},
    {0x0d, 0x00}, //window_height [8]
    {0x0e, 0xf8}, //window_height [7:0] 248
    {0x0f, 0x01}, //window_width [9:8]
    {0x10, 0x48}, //window_width [7:0]  328

    {0x55, 0x00},
    {0x56, 0xf0}, // 240
    {0x57, 0x01},
    {0x58, 0x40}, // 320

    /*SPI*/
    {0xfe, 0x03},
    {0x5b, 0x40},
    {0x5c, 0x01},
    {0x5d, 0xf0},
    {0x5e, 0x00},

    /*AEC*/
    {0xfe, 0x01},
    {0x25, 0x00}, //step
    {0x26, 0x63},
    {0x27, 0x01},
    {0x28, 0x29},
    {0x29, 0x01},
    {0x2a, 0x29},
    {0x2b, 0x01},
    {0x2c, 0x29},
    {0x2d, 0x01},
    {0x2e, 0x29},
    {0x2f, 0x01},
    {0x30, 0x29},
    {0x31, 0x01},
    {0x32, 0x29},
    {0x33, 0x01},
    {0x34, 0x29},
    {0x3c, 0x00},

    /*measure window*/
    {0xfe, 0x01},
    {0xcc, 0x04},
    {0xcd, 0x04},
    {0xce, 0x72},
    {0xcf, 0x52},
    {REGLIST_TAIL, 0x00},
};

#endif
