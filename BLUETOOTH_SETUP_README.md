# ESP32-S3 蓝牙通信设置指南

本指南将帮助您在ESP32-S3 PICO和ESP32-S3 DevKitC之间建立蓝牙连接。

## 硬件要求

- ESP32-S3 PICO-1 开发板 (作为发送端)
- ESP32-S3 DevKitC-1 开发板 (作为接收端)
- USB数据线 x2

## 软件配置

### 1. PICO端配置 (发送端)

#### 编译和上传
```bash
# 使用PICO蓝牙环境编译
pio run -e esp32s3-pico-bluetooth

# 上传到PICO开发板
pio run -e esp32s3-pico-bluetooth -t upload

# 监视串口输出
pio device monitor -e esp32s3-pico-bluetooth
```

#### 代码文件
- 主要代码文件: `src/bluetooth_sender_pico.cpp`
- 功能: 作为BLE服务器，广播数据并等待连接

#### PICO端功能
- 创建BLE服务器
- 生成模拟传感器数据 (温度、湿度、光照、运动检测)
- 每秒通过蓝牙发送JSON格式数据
- 支持自动重连

### 2. DevKitC端配置 (接收端)

#### 编译和上传
```bash
# 使用DevKitC蓝牙环境编译
pio run -e esp32s3-devkitc-bluetooth

# 上传到DevKitC开发板
pio run -e esp32s3-devkitc-bluetooth -t upload

# 监视串口输出
pio device monitor -e esp32s3-devkitc-bluetooth
```

#### 代码文件
- 主要代码文件: `src/bluetooth_receiver_devkitc.cpp`
- 功能: 作为BLE客户端，扫描并连接到PICO端

#### DevKitC端功能
- 扫描BLE设备
- 连接到PICO端的BLE服务器
- 接收并解析JSON数据
- 根据接收到的数据控制LED和输出警告

## 使用步骤

### 第一步：准备PICO端
1. 将`src/bluetooth_sender_pico.cpp`的内容复制到您的PICO项目的main.cpp中
2. 编译并上传到ESP32-S3 PICO
3. 打开串口监视器，应该看到"蓝牙服务已启动，等待连接..."

### 第二步：准备DevKitC端
1. 将`src/bluetooth_receiver_devkitc.cpp`的内容复制到您的DevKitC项目的main.cpp中
2. 编译并上传到ESP32-S3 DevKitC
3. 打开串口监视器，应该看到"开始扫描BLE设备..."

### 第三步：建立连接
1. 确保两个设备都已上电并运行
2. DevKitC会自动扫描并连接到PICO
3. 连接成功后，您将看到数据传输开始

## 数据格式

发送的JSON数据格式：
```json
{
  "device": "ESP32-S3-PICO",
  "timestamp": 12345678,
  "temperature": 25.67,
  "humidity": 65.43,
  "light_level": 456,
  "motion_detected": 1,
  "status": "Motion Detected",
  "battery_level": 85
}
```

## 故障排除

### 连接问题
- 确保两个设备的UUID匹配
- 检查蓝牙是否正确初始化
- 重启设备并重新尝试连接

### 数据传输问题
- 检查JSON格式是否正确
- 确认特征值的读写权限
- 监视串口输出查看错误信息

### 性能优化
- 调整数据发送间隔 (DATA_SEND_INTERVAL)
- 优化JSON数据大小
- 检查内存使用情况

## 自定义修改

### 修改数据内容
在`generateSensorData()`函数中修改要发送的数据：
```cpp
SensorData generateSensorData() {
    SensorData data;
    // 添加您的传感器读取代码
    data.temperature = readTemperatureSensor();
    data.humidity = readHumiditySensor();
    // ...
    return data;
}
```

### 修改数据处理
在`processReceivedData()`函数中添加您的数据处理逻辑：
```cpp
void processReceivedData() {
    if (!lastReceivedData.data_valid) return;
    
    // 添加您的数据处理代码
    if (lastReceivedData.temperature > 30.0) {
        // 温度过高处理
    }
    // ...
}
```

## 注意事项

1. 确保两个设备使用相同的服务和特征UUID
2. BLE连接范围通常在10米以内
3. 避免频繁的连接/断开操作
4. 监控内存使用，避免内存泄漏
5. 在生产环境中添加适当的错误处理和重连机制

## 技术支持

如果遇到问题，请检查：
- PlatformIO环境配置
- 库依赖是否正确安装
- 硬件连接是否正常
- 串口输出的错误信息
