/*
 * ESP32-S3 DevKitC 蓝牙接收端
 * 用于接收来自ESP32-S3 PICO的数据
 */

#include <Arduino.h>
#include "BLEDevice.h"
#include "BLEUtils.h"
#include "BLEScan.h"
#include "BLEAdvertisedDevice.h"
#include "BLEClient.h"
#include <ArduinoJson.h>

// 蓝牙服务和特征UUID (必须与发送端一致)
#define SERVICE_UUID        "12345678-1234-1234-1234-123456789abc"
#define CHARACTERISTIC_UUID "*************-4321-4321-cba987654321"

// 蓝牙相关变量
static boolean doConnect = false;
static boolean connected = false;
static boolean doScan = false;
static BLERemoteCharacteristic* pRemoteCharacteristic;
static BLEAdvertisedDevice* myDevice;
static BLEClient* pClient;

// 数据接收相关
struct ReceivedData {
    String device_name;
    unsigned long timestamp;
    float temperature;
    float humidity;
    float light_level;
    int motion_detected;
    String status;
    int battery_level;
    bool data_valid;
};

ReceivedData lastReceivedData = {"", 0, 0.0, 0.0, 0.0, 0, "", 0, false};

// 数据接收回调函数
static void notifyCallback(
  BLERemoteCharacteristic* pBLERemoteCharacteristic,
  uint8_t* pData,
  size_t length,
  bool isNotify) {
    
    Serial.print("接收到数据: ");
    String receivedString = "";
    for (int i = 0; i < length; i++) {
        receivedString += (char)pData[i];
    }
    Serial.println(receivedString);
    
    // 解析JSON数据
    DynamicJsonDocument doc(512);
    DeserializationError error = deserializeJson(doc, receivedString);
    
    if (error) {
        Serial.print("JSON解析失败: ");
        Serial.println(error.c_str());
        return;
    }
    
    // 提取数据
    lastReceivedData.device_name = doc["device"].as<String>();
    lastReceivedData.timestamp = doc["timestamp"];
    lastReceivedData.temperature = doc["temperature"];
    lastReceivedData.humidity = doc["humidity"];
    lastReceivedData.light_level = doc["light_level"];
    lastReceivedData.motion_detected = doc["motion_detected"];
    lastReceivedData.status = doc["status"].as<String>();
    lastReceivedData.battery_level = doc["battery_level"];
    lastReceivedData.data_valid = true;
    
    // 输出解析后的数据
    Serial.println("--- 解析后的数据 ---");
    Serial.printf("设备: %s\n", lastReceivedData.device_name.c_str());
    Serial.printf("时间戳: %lu\n", lastReceivedData.timestamp);
    Serial.printf("温度: %.2f°C\n", lastReceivedData.temperature);
    Serial.printf("湿度: %.2f%%\n", lastReceivedData.humidity);
    Serial.printf("光照: %.0f lux\n", lastReceivedData.light_level);
    Serial.printf("运动检测: %s\n", lastReceivedData.motion_detected ? "是" : "否");
    Serial.printf("状态: %s\n", lastReceivedData.status.c_str());
    Serial.printf("电池电量: %d%%\n", lastReceivedData.battery_level);
    Serial.println("-------------------");
}

// 客户端回调
class MyClientCallback : public BLEClientCallbacks {
  void onConnect(BLEClient* pclient) {
    Serial.println("客户端已连接");
  }

  void onDisconnect(BLEClient* pclient) {
    connected = false;
    Serial.println("客户端已断开连接");
  }
};

// 连接到服务器
bool connectToServer() {
    Serial.print("正在连接到设备: ");
    Serial.println(myDevice->getAddress().toString().c_str());
    
    pClient = BLEDevice::createClient();
    Serial.println(" - 已创建客户端");

    pClient->setClientCallbacks(new MyClientCallback());

    // 连接到远程BLE服务器
    pClient->connect(myDevice);
    Serial.println(" - 已连接到服务器");

    // 获取远程服务的引用
    BLERemoteService* pRemoteService = pClient->getService(SERVICE_UUID);
    if (pRemoteService == nullptr) {
        Serial.print("未找到服务UUID: ");
        Serial.println(SERVICE_UUID);
        pClient->disconnect();
        return false;
    }
    Serial.println(" - 已找到服务");

    // 获取远程特征的引用
    pRemoteCharacteristic = pRemoteService->getCharacteristic(CHARACTERISTIC_UUID);
    if (pRemoteCharacteristic == nullptr) {
        Serial.print("未找到特征UUID: ");
        Serial.println(CHARACTERISTIC_UUID);
        pClient->disconnect();
        return false;
    }
    Serial.println(" - 已找到特征");

    // 读取特征值
    if(pRemoteCharacteristic->canRead()) {
        std::string value = pRemoteCharacteristic->readValue();
        Serial.print("特征值: ");
        Serial.println(value.c_str());
    }

    // 注册通知回调
    if(pRemoteCharacteristic->canNotify())
        pRemoteCharacteristic->registerForNotify(notifyCallback);

    connected = true;
    Serial.println("连接成功！");
    return true;
}

// 扫描回调
class MyAdvertisedDeviceCallbacks: public BLEAdvertisedDeviceCallbacks {
    void onResult(BLEAdvertisedDevice advertisedDevice) {
        Serial.print("发现BLE设备: ");
        Serial.println(advertisedDevice.toString().c_str());

        // 检查是否是我们要找的设备
        if (advertisedDevice.haveServiceUUID() && advertisedDevice.isAdvertisingService(BLEUUID(SERVICE_UUID))) {
            BLEDevice::getScan()->stop();
            myDevice = new BLEAdvertisedDevice(advertisedDevice);
            doConnect = true;
            doScan = true;
            Serial.println("找到目标设备！");
        }
    }
};

// 初始化蓝牙扫描
void initBluetoothScan() {
    Serial.println("初始化蓝牙扫描...");
    
    BLEDevice::init("ESP32-S3-DevKitC-Receiver");

    // 获取扫描对象并设置回调
    BLEScan* pBLEScan = BLEDevice::getScan();
    pBLEScan->setAdvertisedDeviceCallbacks(new MyAdvertisedDeviceCallbacks());
    pBLEScan->setInterval(1349);
    pBLEScan->setWindow(449);
    pBLEScan->setActiveScan(true);
    pBLEScan->start(5, false);
    
    Serial.println("开始扫描BLE设备...");
}

// 处理接收到的数据
void processReceivedData() {
    if (!lastReceivedData.data_valid) {
        return;
    }
    
    // 在这里可以添加对接收到数据的处理逻辑
    // 例如：控制LED、驱动电机、发送到云端等
    
    // 示例：根据运动检测状态控制内置LED
    if (lastReceivedData.motion_detected) {
        digitalWrite(LED_BUILTIN, HIGH);
    } else {
        digitalWrite(LED_BUILTIN, LOW);
    }
    
    // 示例：温度报警
    if (lastReceivedData.temperature > 28.0) {
        Serial.println("⚠️ 温度过高警告！");
    }
    
    // 示例：湿度报警
    if (lastReceivedData.humidity > 70.0) {
        Serial.println("⚠️ 湿度过高警告！");
    }
    
    // 示例：电池电量低报警
    if (lastReceivedData.battery_level < 30) {
        Serial.println("🔋 电池电量低警告！");
    }
    
    // 重置数据有效标志
    lastReceivedData.data_valid = false;
}

void setup() {
    // 初始化串口
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("=================================");
    Serial.println("ESP32-S3 DevKitC 蓝牙接收端启动");
    Serial.println("=================================");
    
    // 初始化内置LED
    pinMode(LED_BUILTIN, OUTPUT);
    digitalWrite(LED_BUILTIN, LOW);
    
    // 初始化蓝牙扫描
    initBluetoothScan();
    
    Serial.println("系统初始化完成");
}

void loop() {
    // 如果需要连接且还未连接
    if (doConnect == true) {
        if (connectToServer()) {
            Serial.println("连接成功，开始接收数据");
        } else {
            Serial.println("连接失败");
        }
        doConnect = false;
    }

    // 如果连接断开，重新开始扫描
    if (!connected && doScan) {
        BLEDevice::getScan()->start(0);
    }
    
    // 处理接收到的数据
    processReceivedData();
    
    // 输出连接状态
    static unsigned long lastStatusTime = 0;
    if (millis() - lastStatusTime > 10000) {
        Serial.printf("连接状态: %s\n", connected ? "已连接" : "未连接");
        Serial.printf("自由堆内存: %d bytes\n", ESP.getFreeHeap());
        lastStatusTime = millis();
    }
    
    delay(1000);
}
