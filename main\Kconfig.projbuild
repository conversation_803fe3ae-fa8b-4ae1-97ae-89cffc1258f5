menu "USB WebCam config"

    config CAMERA_XCLK_FREQ
        int "XCLK frequency"
        range 1000000 40000000
        default 20000000
        help
            Select XCLK frequency.

    menu "Camera Pin Configuration"

        choice CAMERA_MODULE
            bool "Select Camera Pinout"
            default CAMERA_MODULE_M5STACK_ATOMS3R_CAM if IDF_TARGET_ESP32S3
            default CAMERA_MODULE_ESP_S2_KALUGA if IDF_TARGET_ESP32S2
            help
                Select Camera Pinout.

            config CAMERA_MODULE_ESP_S2_KALUGA
                bool "ESP32-S2-Kaluga-1 V1.3"
            config CAMERA_MODULE_ESP_S3_EYE
                bool "ESP-S3-EYE DevKit"
            config CAMERA_MODULE_ESP_S3_KORVO2
                bool "ESP-S3-Korvo-2"
            config CAMERA_MODULE_M5STACK_ATOMS3R_CAM
                bool "M5Stack-AtomS3R-CAM"
            config CAMERA_MODULE_CUSTOM
                bool "Custom Camera Pinout"
        endchoice

        config CAMERA_PIN_PWDN
            depends on CAMERA_MODULE_CUSTOM
            int "Power Down pin"
            range -1 33
            default -1
            help
                Select Power Down pin or -1 for unmanaged.

        config CAMERA_PIN_RESET
            depends on CAMERA_MODULE_CUSTOM
            int "Reset pin"
            range -1 33
            default -1
            help
                Select Camera Reset pin or -1 for software reset.

        config CAMERA_PIN_XCLK
            depends on CAMERA_MODULE_CUSTOM
            int "XCLK pin"
            range 0 33
            default 21
            help
                Select Camera XCLK pin.

        config CAMERA_PIN_SIOD
            depends on CAMERA_MODULE_CUSTOM
            int "SIOD pin"
            range 0 33
            default 26
            help
                Select Camera SIOD pin.

        config CAMERA_PIN_SIOC
            depends on CAMERA_MODULE_CUSTOM
            int "SIOC pin"
            range 0 33
            default 27
            help
                Select Camera SIOC pin.

        config CAMERA_PIN_VSYNC
            depends on CAMERA_MODULE_CUSTOM
            int "VSYNC pin"
            range 0 39
            default 25
            help
                Select Camera VSYNC pin.

        config CAMERA_PIN_HREF
            depends on CAMERA_MODULE_CUSTOM
            int "HREF pin"
            range 0 39
            default 23
            help
                Select Camera HREF pin.

        config CAMERA_PIN_PCLK
            depends on CAMERA_MODULE_CUSTOM
            int "PCLK pin"
            range 0 39
            default 25
            help
                Select Camera PCLK pin.

        config CAMERA_PIN_Y2
            depends on CAMERA_MODULE_CUSTOM
            int "Y2 pin"
            range 0 39
            default 4
            help
                Select Camera Y2 pin.

        config CAMERA_PIN_Y3
            depends on CAMERA_MODULE_CUSTOM
            int "Y3 pin"
            range 0 39
            default 5
            help
                Select Camera Y3 pin.

        config CAMERA_PIN_Y4
            depends on CAMERA_MODULE_CUSTOM
            int "Y4 pin"
            range 0 39
            default 18
            help
                Select Camera Y4 pin.

        config CAMERA_PIN_Y5
            depends on CAMERA_MODULE_CUSTOM
            int "Y5 pin"
            range 0 39
            default 19
            help
                Select Camera Y5 pin.

        config CAMERA_PIN_Y6
            depends on CAMERA_MODULE_CUSTOM
            int "Y6 pin"
            range 0 39
            default 36
            help
                Select Camera Y6 pin.

        config CAMERA_PIN_Y7
            depends on CAMERA_MODULE_CUSTOM
            int "Y7 pin"
            range 0 39
            default 39
            help
                Select Camera Y7 pin.

        config CAMERA_PIN_Y8
            depends on CAMERA_MODULE_CUSTOM
            int "Y8 pin"
            range 0 39
            default 34
            help
                Select Camera Y8 pin.

        config CAMERA_PIN_Y9
            depends on CAMERA_MODULE_CUSTOM
            int "Y9 pin"
            range 0 39
            default 35
            help
                Select Camera Y9 pin.
    endmenu
endmenu
