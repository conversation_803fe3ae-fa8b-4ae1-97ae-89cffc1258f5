@echo off
echo ================================
echo ATOMS3R-CAM 固件上传工具
echo ================================

echo 正在编译项目...
pio run

if %ERRORLEVEL% NEQ 0 (
    echo 编译失败！请检查代码错误。
    pause
    exit /b 1
)

echo.
echo 编译成功！正在上传固件...
echo 请确保设备已连接到 COM10 端口

:: 只上传固件，不进行验证
esptool.py --chip esp32s3 --port COM10 --baud 921600 write_flash -z 0x0 .pio\build\m5stack-atoms3r\firmware.bin

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ================================
    echo 固件上传成功！
    echo ================================
    echo.
    echo 现在可以：
    echo 1. 按设备上的复位按钮重启
    echo 2. 打开串口监视器查看输出
    echo 3. 发送命令测试摄像头功能
    echo.
    echo 串口命令：
    echo   c - 捕获图像
    echo   s - 显示状态
    echo   r - 重置摄像头
    echo.
) else (
    echo 上传失败！请检查连接和端口。
)

pause
