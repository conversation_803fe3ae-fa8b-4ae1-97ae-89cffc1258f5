version: "1.1.0"
targets:
  - esp32s2
  - esp32s3
  - esp32p4

description: USB Device UVC, Streaming Video to Host
url: https://github.com/espressif/esp-iot-solution/tree/master/components/usb/usb_device_uvc
documentation: https://docs.espressif.com/projects/esp-iot-solution/en/latest/usb/usb_device/usb_device_uvc.html
repository: https://github.com/espressif/esp-iot-solution.git
issues: https://github.com/espressif/esp-iot-solution/issues

dependencies:
  idf: ">=5.0"
  espressif/tinyusb:
    version: ">=0.15.0~10"
  cmake_utilities: "*"
examples:
  - path: ../../../examples/usb/device/usb_webcam
  - path: ../../../examples/usb/device/usb_dual_uvc_device
