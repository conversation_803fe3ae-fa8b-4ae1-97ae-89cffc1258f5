/*
 * SPDX-FileCopyrightText: 2024 M5Stack Technology CO LTD
 *
 * SPDX-License-Identifier: MIT
 */

#pragma once

// M5Stack ATOMS3R-CAM 摄像头引脚定义
#define CAMERA_MODULE_NAME "M5STACK AtomS3R-CAM"
#define CAMERA_MODULE_SOC  "esp32s3"

// 摄像头控制引脚
#define CAMERA_PIN_PWDN    -1
#define CAMERA_PIN_RESET   -1

// 时钟和同步引脚
#define CAMERA_PIN_VSYNC   10
#define CAMERA_PIN_HREF    14
#define CAMERA_PIN_PCLK    40
#define CAMERA_PIN_XCLK    21

// I2C通信引脚
#define CAMERA_PIN_SIOD    12  // SDA
#define CAMERA_PIN_SIOC    9   // SCL

// 数据引脚 (8位并行数据)
#define CAMERA_PIN_D0      3
#define CAMERA_PIN_D1      42
#define CAMERA_PIN_D2      46
#define CAMERA_PIN_D3      48
#define CAMERA_PIN_D4      4
#define CAMERA_PIN_D5      17
#define CAMERA_PIN_D6      11
#define CAMERA_PIN_D7      13

// 其他硬件引脚定义
#define HAL_PIN_I2C_INTER_SDA 45
#define HAL_PIN_I2C_INTER_SCL 0
#define HAL_PIN_BUTTON_A      8
#define HAL_PIN_IR_TX         47

// 摄像头电源控制引脚
#define CAMERA_POWER_PIN      18
