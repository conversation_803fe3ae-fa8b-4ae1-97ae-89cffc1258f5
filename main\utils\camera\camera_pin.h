/*
 * SPDX-FileCopyrightText: 2022-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#pragma once

#include "sdkconfig.h"

#if CONFIG_CAMERA_MODULE_ESP_S2_KALUGA
#define CAMERA_MODULE_NAME "ESP-S2-KALUGA"
#define CAMERA_MODULE_SOC  "esp32s2"
#define CAMERA_PIN_PWDN    -1
#define CAMERA_PIN_RESET   -1
#define CAMERA_PIN_XCLK    1
#define CAMERA_PIN_SIOD    8
#define CAMERA_PIN_SIOC    7

#define CAMERA_PIN_D7    38
#define CAMERA_PIN_D6    21
#define CAMERA_PIN_D5    40
#define CAMERA_PIN_D4    39
#define CAMERA_PIN_D3    42
#define CAMERA_PIN_D2    41
#define CAMERA_PIN_D1    37
#define CAMERA_PIN_D0    36
#define CAMERA_PIN_VSYNC 2
#define CAMERA_PIN_HREF  3
#define CAMERA_PIN_PCLK  33

#elif CONFIG_CAMERA_MODULE_ESP_S3_EYE
#define CAMERA_MODULE_NAME "ESP-S3-EYE"
#define CAMERA_MODULE_SOC  "esp32s3"
#define CAMERA_PIN_PWDN    -1
#define CAMERA_PIN_RESET   -1

#define CAMERA_PIN_VSYNC 6
#define CAMERA_PIN_HREF  7
#define CAMERA_PIN_PCLK  13
#define CAMERA_PIN_XCLK  15

#define CAMERA_PIN_SIOD 4
#define CAMERA_PIN_SIOC 5

#define CAMERA_PIN_D0 11
#define CAMERA_PIN_D1 9
#define CAMERA_PIN_D2 8
#define CAMERA_PIN_D3 10
#define CAMERA_PIN_D4 12
#define CAMERA_PIN_D5 18
#define CAMERA_PIN_D6 17
#define CAMERA_PIN_D7 16

#elif CONFIG_CAMERA_MODULE_ESP_S3_KORVO2
#define CAMERA_MODULE_NAME "ESP_S3_KORVO2"
#define CAMERA_MODULE_SOC  "esp32s3"
#define CAMERA_PIN_PWDN    -1
#define CAMERA_PIN_RESET   -1

#define CAMERA_PIN_VSYNC 21
#define CAMERA_PIN_HREF  38
#define CAMERA_PIN_PCLK  11
#define CAMERA_PIN_XCLK  40

#define CAMERA_PIN_SIOD 17
#define CAMERA_PIN_SIOC 18

#define CAMERA_PIN_D0 13
#define CAMERA_PIN_D1 47
#define CAMERA_PIN_D2 14
#define CAMERA_PIN_D3 3
#define CAMERA_PIN_D4 12
#define CAMERA_PIN_D5 42
#define CAMERA_PIN_D6 41
#define CAMERA_PIN_D7 39

#elif CONFIG_CAMERA_MODULE_M5STACK_ATOMS3R_CAM
#define CAMERA_MODULE_NAME "M5STACK AtomS3R-CAM"
#define CAMERA_MODULE_SOC  "esp32s3"
#define CAMERA_PIN_PWDN    -1
#define CAMERA_PIN_RESET   -1

#define CAMERA_PIN_VSYNC 10
#define CAMERA_PIN_HREF  14
#define CAMERA_PIN_PCLK  40
#define CAMERA_PIN_XCLK  21

#define CAMERA_PIN_SIOD 12
#define CAMERA_PIN_SIOC 9

#define CAMERA_PIN_D0 3
#define CAMERA_PIN_D1 42
#define CAMERA_PIN_D2 46
#define CAMERA_PIN_D3 48
#define CAMERA_PIN_D4 4
#define CAMERA_PIN_D5 17
#define CAMERA_PIN_D6 11
#define CAMERA_PIN_D7 13

#elif CONFIG_CAMERA_MODULE_CUSTOM
#define CAMERA_MODULE_NAME "CUSTOM"
#define CAMERA_MODULE_SOC  "custom"
#define CAMERA_PIN_PWDN    CONFIG_CAMERA_PIN_PWDN
#define CAMERA_PIN_RESET   CONFIG_CAMERA_PIN_RESET
#define CAMERA_PIN_XCLK    CONFIG_CAMERA_PIN_XCLK
#define CAMERA_PIN_SIOD    CONFIG_CAMERA_PIN_SIOD
#define CAMERA_PIN_SIOC    CONFIG_CAMERA_PIN_SIOC

#define CAMERA_PIN_D7    CONFIG_CAMERA_PIN_Y9
#define CAMERA_PIN_D6    CONFIG_CAMERA_PIN_Y8
#define CAMERA_PIN_D5    CONFIG_CAMERA_PIN_Y7
#define CAMERA_PIN_D4    CONFIG_CAMERA_PIN_Y6
#define CAMERA_PIN_D3    CONFIG_CAMERA_PIN_Y5
#define CAMERA_PIN_D2    CONFIG_CAMERA_PIN_Y4
#define CAMERA_PIN_D1    CONFIG_CAMERA_PIN_Y3
#define CAMERA_PIN_D0    CONFIG_CAMERA_PIN_Y2
#define CAMERA_PIN_VSYNC CONFIG_CAMERA_PIN_VSYNC
#define CAMERA_PIN_HREF  CONFIG_CAMERA_PIN_HREF
#define CAMERA_PIN_PCLK  CONFIG_CAMERA_PIN_PCLK
#endif
