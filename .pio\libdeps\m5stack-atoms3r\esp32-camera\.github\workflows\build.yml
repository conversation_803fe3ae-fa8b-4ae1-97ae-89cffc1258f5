name: Build examples
on:
  push:
    branches:
    - master
  pull_request:

jobs:
  build-examples:
    name: Build for ${{ matrix.idf_target }} on ${{ matrix.idf_ver }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        idf_ver: ["release-v4.1", "release-v4.2", "release-v4.3"]
        idf_target: ["esp32", "esp32s2"]
        exclude:
          - idf_ver: "release-v4.1"
            idf_target: esp32s2 # ESP32S2 support started with version 4.2
    container: espressif/idf:${{ matrix.idf_ver }}
    steps:
      - uses: actions/checkout@v1
        with:
          submodules: 'true'
      - name: esp-idf build
        env:
          IDF_TARGET: ${{ matrix.idf_target }}
        shell: bash
        working-directory: examples
        run: |
          . ${IDF_PATH}/export.sh
          idf.py build
  build-examples-pedantic:
    name: Build for ${{ matrix.idf_target }} on ${{ matrix.idf_ver }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        idf_ver: ["release-v4.4", "release-v5.0", "release-v5.1", "latest"]
        idf_target: ["esp32", "esp32s2", "esp32s3", "esp32c2", "esp32c3"]
        exclude:
          - idf_ver: "release-v4.4"
            idf_target: esp32c2 # ESP32C2 support started with version 5.0
    container: espressif/idf:${{ matrix.idf_ver }}
    steps:
      - uses: actions/checkout@v1
        with:
          submodules: 'true'
      - name: esp-idf build
        env:
          IDF_TARGET: ${{ matrix.idf_target }}
        shell: bash
        working-directory: examples
        run: |
          . ${IDF_PATH}/export.sh
          export PEDANTIC_FLAGS="-DIDF_CI_BUILD -Werror -Werror=deprecated-declarations -Werror=unused-variable -Werror=unused-but-set-variable -Werror=unused-function"
          export EXTRA_CFLAGS="${PEDANTIC_FLAGS} -Wstrict-prototypes"
          export EXTRA_CXXFLAGS="${PEDANTIC_FLAGS}"
          idf.py build
