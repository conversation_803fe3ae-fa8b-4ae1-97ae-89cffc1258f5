#include "wifi_serial_bridge.h"
#include <cstdarg>

// 全局实例
WiFiSerialBridge wifiSerial;

// 构造函数
WiFiSerialBridge::WiFiSerialBridge() {
    server = nullptr;
    client_count = 0;
    target_port = SERIAL_BRIDGE_PORT;

    server_task_handle = nullptr;
    client_task_handle = nullptr;
    serial_task_handle = nullptr;

    serial_tx_queue = nullptr;
    serial_rx_queue = nullptr;

    server_status = STATUS_DISCONNECTED;
    client_status = STATUS_DISCONNECTED;
    is_server_mode = false;
    is_client_mode = false;

    bytes_sent = 0;
    bytes_received = 0;
    packets_sent = 0;
    packets_received = 0;
    last_heartbeat = 0;
}

// 析构函数
WiFiSerialBridge::~WiFiSerialBridge() {
    end();
}

// 初始化
bool WiFiSerialBridge::begin() {
    // 创建队列
    serial_tx_queue = xQueueCreate(WIFI_SERIAL_QUEUE_SIZE, sizeof(SerialPacket));
    serial_rx_queue = xQueueCreate(WIFI_SERIAL_QUEUE_SIZE, sizeof(SerialPacket));

    if (!serial_tx_queue || !serial_rx_queue) {
        Serial.println("WiFiSerial: Failed to create queues");
        return false;
    }

    // 创建串口处理任务
    BaseType_t result = xTaskCreatePinnedToCore(
        serialTask,
        "WiFiSerialTask",
        4096,
        this,
        1,
        &serial_task_handle,
        0  // 绑定到核心0
    );

    if (result != pdPASS) {
        Serial.println("WiFiSerial: Failed to create serial task");
        return false;
    }

    Serial.println("WiFiSerial: Initialized successfully");
    return true;
}

// 结束
void WiFiSerialBridge::end() {
    stopServer();
    stopClient();

    // 删除任务
    if (serial_task_handle) {
        vTaskDelete(serial_task_handle);
        serial_task_handle = nullptr;
    }

    // 删除队列
    if (serial_tx_queue) {
        vQueueDelete(serial_tx_queue);
        serial_tx_queue = nullptr;
    }
    if (serial_rx_queue) {
        vQueueDelete(serial_rx_queue);
        serial_rx_queue = nullptr;
    }
}

// 启动服务器模式
bool WiFiSerialBridge::startServer(uint16_t port) {
    if (is_server_mode) {
        Serial.println("WiFiSerial: Server already running");
        return true;
    }

    server = new WiFiServer(port);
    if (!server) {
        Serial.println("WiFiSerial: Failed to create server");
        return false;
    }

    server->begin();
    is_server_mode = true;
    server_status = STATUS_CONNECTING;

    // 创建服务器任务
    BaseType_t result = xTaskCreatePinnedToCore(
        serverTask,
        "WiFiServerTask",
        4096,
        this,
        2,
        &server_task_handle,
        1  // 绑定到核心1
    );

    if (result != pdPASS) {
        Serial.println("WiFiSerial: Failed to create server task");
        delete server;
        server = nullptr;
        is_server_mode = false;
        server_status = STATUS_ERROR;
        return false;
    }

    Serial.printf("WiFiSerial: Server started on port %d\n", port);
    return true;
}

// 停止服务器
void WiFiSerialBridge::stopServer() {
    if (!is_server_mode) return;

    is_server_mode = false;
    server_status = STATUS_DISCONNECTED;

    // 断开所有客户端
    for (int i = 0; i < client_count; i++) {
        if (clients[i].connected()) {
            clients[i].stop();
        }
    }
    client_count = 0;

    // 删除服务器任务
    if (server_task_handle) {
        vTaskDelete(server_task_handle);
        server_task_handle = nullptr;
    }

    // 停止服务器
    if (server) {
        server->end();
        delete server;
        server = nullptr;
    }

    Serial.println("WiFiSerial: Server stopped");
}

// 启动客户端模式
bool WiFiSerialBridge::startClient(const String& ip, uint16_t port) {
    if (is_client_mode) {
        Serial.println("WiFiSerial: Client already running");
        return true;
    }

    target_ip = ip;
    target_port = port;
    is_client_mode = true;
    client_status = STATUS_CONNECTING;

    // 创建客户端任务
    BaseType_t result = xTaskCreatePinnedToCore(
        clientTask,
        "WiFiClientTask",
        4096,
        this,
        2,
        &client_task_handle,
        1  // 绑定到核心1
    );

    if (result != pdPASS) {
        Serial.println("WiFiSerial: Failed to create client task");
        is_client_mode = false;
        client_status = STATUS_ERROR;
        return false;
    }

    Serial.printf("WiFiSerial: Client started, connecting to %s:%d\n", ip.c_str(), port);
    return true;
}

// 停止客户端
void WiFiSerialBridge::stopClient() {
    if (!is_client_mode) return;

    is_client_mode = false;
    client_status = STATUS_DISCONNECTED;

    // 断开连接
    if (client.connected()) {
        client.stop();
    }

    // 删除客户端任务
    if (client_task_handle) {
        vTaskDelete(client_task_handle);
        client_task_handle = nullptr;
    }

    Serial.println("WiFiSerial: Client stopped");
}

// 双向模式
bool WiFiSerialBridge::startBidirectional(const String& target_ip, uint16_t port) {
    bool server_ok = startServer(port);
    bool client_ok = startClient(target_ip, port);
    return server_ok && client_ok;
}

// 计算校验和
uint16_t WiFiSerialBridge::calculateChecksum(const uint8_t* data, size_t length) {
    uint16_t checksum = 0;
    for (size_t i = 0; i < length; i++) {
        checksum += data[i];
    }
    return checksum;
}

// 验证数据包
bool WiFiSerialBridge::validatePacket(const SerialPacket* packet) {
    if (!packet) return false;
    if (packet->length > SERIAL_BUFFER_SIZE) return false;

    uint16_t calculated_checksum = calculateChecksum(packet->data, packet->length);
    return calculated_checksum == packet->checksum;
}

// 发送数据包
void WiFiSerialBridge::sendPacket(WiFiClient& client, PacketType type, const uint8_t* data, size_t length) {
    if (!client.connected() || length > SERIAL_BUFFER_SIZE) return;

    SerialPacket packet;
    packet.type = type;
    packet.length = length;
    packet.timestamp = millis();

    if (data && length > 0) {
        memcpy(packet.data, data, length);
    }

    packet.checksum = calculateChecksum(packet.data, packet.length);

    // 发送数据包
    size_t sent = client.write((uint8_t*)&packet, sizeof(SerialPacket));
    if (sent == sizeof(SerialPacket)) {
        bytes_sent += sent;
        packets_sent++;
    }
}

// 接收数据包
bool WiFiSerialBridge::receivePacket(WiFiClient& client, SerialPacket* packet) {
    if (!client.connected() || !packet) return false;

    if (client.available() >= sizeof(SerialPacket)) {
        size_t received = client.readBytes((uint8_t*)packet, sizeof(SerialPacket));
        if (received == sizeof(SerialPacket)) {
            bytes_received += received;
            packets_received++;
            return validatePacket(packet);
        }
    }
    return false;
}

// 数据发送接口
size_t WiFiSerialBridge::write(const uint8_t* data, size_t length) {
    if (!data || length == 0) return 0;

    SerialPacket packet;
    packet.type = PACKET_SERIAL_DATA;
    packet.length = min(length, (size_t)SERIAL_BUFFER_SIZE);
    packet.timestamp = millis();
    memcpy(packet.data, data, packet.length);
    packet.checksum = calculateChecksum(packet.data, packet.length);

    // 发送到队列
    if (serial_tx_queue && xQueueSend(serial_tx_queue, &packet, 0) == pdTRUE) {
        return packet.length;
    }
    return 0;
}

size_t WiFiSerialBridge::write(const String& data) {
    return write((const uint8_t*)data.c_str(), data.length());
}

size_t WiFiSerialBridge::println(const String& data) {
    String line = data + "\n";
    return write(line);
}

size_t WiFiSerialBridge::printf(const char* format, ...) {
    char buffer[512];  // 临时缓冲区
    va_list args;
    va_start(args, format);
    int len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    if (len > 0 && len < sizeof(buffer)) {
        return write((const uint8_t*)buffer, len);
    }
    return 0;
}

// 数据接收接口
int WiFiSerialBridge::available() {
    return (serial_rx_queue) ? uxQueueMessagesWaiting(serial_rx_queue) : 0;
}

int WiFiSerialBridge::read() {
    if (!serial_rx_queue || uxQueueMessagesWaiting(serial_rx_queue) == 0) {
        return -1;
    }

    SerialPacket packet;
    static uint16_t read_pos = 0;
    static SerialPacket current_packet;
    static bool has_packet = false;

    if (!has_packet) {
        if (xQueueReceive(serial_rx_queue, &current_packet, 0) == pdTRUE) {
            has_packet = true;
            read_pos = 0;
        } else {
            return -1;
        }
    }

    if (read_pos < current_packet.length) {
        uint8_t byte = current_packet.data[read_pos++];
        if (read_pos >= current_packet.length) {
            has_packet = false;
        }
        return byte;
    }

    has_packet = false;
    return -1;
}

String WiFiSerialBridge::readString() {
    String result;
    while (available()) {
        int c = read();
        if (c >= 0) {
            result += (char)c;
        }
    }
    return result;
}

String WiFiSerialBridge::readStringUntil(char terminator) {
    String result;
    while (available()) {
        int c = read();
        if (c < 0) break;
        if (c == terminator) break;
        result += (char)c;
    }
    return result;
}

// 状态查询
bool WiFiSerialBridge::isServerConnected() {
    return is_server_mode && server_status == STATUS_CONNECTED && client_count > 0;
}

bool WiFiSerialBridge::isClientConnected() {
    return is_client_mode && client_status == STATUS_CONNECTED && client.connected();
}

ConnectionStatus WiFiSerialBridge::getServerStatus() {
    return server_status;
}

ConnectionStatus WiFiSerialBridge::getClientStatus() {
    return client_status;
}

// 统计信息
unsigned long WiFiSerialBridge::getBytesSent() {
    return bytes_sent;
}

unsigned long WiFiSerialBridge::getBytesReceived() {
    return bytes_received;
}

unsigned long WiFiSerialBridge::getPacketsSent() {
    return packets_sent;
}

unsigned long WiFiSerialBridge::getPacketsReceived() {
    return packets_received;
}

void WiFiSerialBridge::resetStatistics() {
    bytes_sent = 0;
    bytes_received = 0;
    packets_sent = 0;
    packets_received = 0;
}

// 调试和监控
void WiFiSerialBridge::printStatus() {
    Serial.println("=== WiFi Serial Bridge Status ===");
    Serial.printf("Server Mode: %s\n", is_server_mode ? "Active" : "Inactive");
    Serial.printf("Server Status: %d\n", server_status);
    Serial.printf("Connected Clients: %d\n", client_count);

    Serial.printf("Client Mode: %s\n", is_client_mode ? "Active" : "Inactive");
    Serial.printf("Client Status: %d\n", client_status);
    Serial.printf("Target: %s:%d\n", target_ip.c_str(), target_port);

    Serial.printf("Statistics: Sent %lu bytes (%lu packets), Received %lu bytes (%lu packets)\n",
                 bytes_sent, packets_sent, bytes_received, packets_received);
}

void WiFiSerialBridge::setDebugMode(bool enabled) {
    // 未实现
}

// 发送心跳包
void WiFiSerialBridge::sendHeartbeat() {
    unsigned long now = millis();
    if (now - last_heartbeat > HEARTBEAT_INTERVAL_MS) {
        last_heartbeat = now;

        // 客户端模式下发送心跳
        if (is_client_mode && client.connected()) {
            uint8_t dummy = 0;
            sendPacket(client, PACKET_HEARTBEAT, &dummy, 1);
        }

        // 服务器模式下发送心跳
        if (is_server_mode) {
            for (int i = 0; i < client_count; i++) {
                if (clients[i].connected()) {
                    uint8_t dummy = 0;
                    sendPacket(clients[i], PACKET_HEARTBEAT, &dummy, 1);
                }
            }
        }
    }
}

// 服务器任务
void WiFiSerialBridge::serverTask(void* parameter) {
    WiFiSerialBridge* bridge = (WiFiSerialBridge*)parameter;

    Serial.println("WiFiSerial: Server task started");

    while (bridge->is_server_mode) {
        // 检查新的客户端连接
        WiFiClient newClient = bridge->server->available();
        if (newClient) {
            if (bridge->client_count < MAX_CLIENTS) {
                bridge->clients[bridge->client_count] = newClient;
                bridge->client_count++;
                bridge->server_status = STATUS_CONNECTED;
                Serial.printf("WiFiSerial: Client connected, total: %d\n", bridge->client_count);
            } else {
                newClient.stop();
                Serial.println("WiFiSerial: Max clients reached, connection rejected");
            }
        }

        // 处理现有客户端
        for (int i = 0; i < bridge->client_count; i++) {
            if (bridge->clients[i].connected()) {
                bridge->handleClientConnection(bridge->clients[i]);
            } else {
                // 移除断开的客户端
                bridge->clients[i].stop();
                for (int j = i; j < bridge->client_count - 1; j++) {
                    bridge->clients[j] = bridge->clients[j + 1];
                }
                bridge->client_count--;
                i--; // 重新检查当前位置
                Serial.printf("WiFiSerial: Client disconnected, remaining: %d\n", bridge->client_count);
            }
        }

        // 发送心跳包
        bridge->sendHeartbeat();

        // 处理发送队列
        SerialPacket packet;
        while (bridge->serial_tx_queue &&
               xQueueReceive(bridge->serial_tx_queue, &packet, 0) == pdTRUE) {
            for (int i = 0; i < bridge->client_count; i++) {
                if (bridge->clients[i].connected()) {
                    bridge->sendPacket(bridge->clients[i], (PacketType)packet.type,
                                     packet.data, packet.length);
                }
            }
        }

        vTaskDelay(10 / portTICK_PERIOD_MS);
    }

    Serial.println("WiFiSerial: Server task ended");
    vTaskDelete(NULL);
}

// 客户端任务
void WiFiSerialBridge::clientTask(void* parameter) {
    WiFiSerialBridge* bridge = (WiFiSerialBridge*)parameter;

    Serial.println("WiFiSerial: Client task started");

    while (bridge->is_client_mode) {
        // 尝试连接
        if (!bridge->client.connected()) {
            bridge->client_status = STATUS_CONNECTING;
            Serial.printf("WiFiSerial: Connecting to %s:%d\n",
                         bridge->target_ip.c_str(), bridge->target_port);

            if (bridge->client.connect(bridge->target_ip.c_str(), bridge->target_port)) {
                bridge->client_status = STATUS_CONNECTED;
                Serial.println("WiFiSerial: Client connected successfully");
            } else {
                bridge->client_status = STATUS_ERROR;
                Serial.println("WiFiSerial: Client connection failed, retrying...");
                vTaskDelay(RECONNECT_DELAY_MS / portTICK_PERIOD_MS);
                continue;
            }
        }

        // 处理连接
        if (bridge->client.connected()) {
            bridge->handleClientConnection(bridge->client);

            // 处理发送队列
            SerialPacket packet;
            while (bridge->serial_tx_queue &&
                   xQueueReceive(bridge->serial_tx_queue, &packet, 0) == pdTRUE) {
                bridge->sendPacket(bridge->client, (PacketType)packet.type,
                                 packet.data, packet.length);
            }
        }

        // 发送心跳包
        bridge->sendHeartbeat();

        vTaskDelay(10 / portTICK_PERIOD_MS);
    }

    Serial.println("WiFiSerial: Client task ended");
    vTaskDelete(NULL);
}

// 处理客户端连接
void WiFiSerialBridge::handleClientConnection(WiFiClient& client) {
    SerialPacket packet;
    while (receivePacket(client, &packet)) {
        switch (packet.type) {
            case PACKET_SERIAL_DATA:
                // 将接收到的数据放入接收队列
                if (serial_rx_queue) {
                    xQueueSend(serial_rx_queue, &packet, 0);
                }
                break;

            case PACKET_HEARTBEAT:
                // 回复心跳包
                {
                    uint8_t dummy = 0;
                    sendPacket(client, PACKET_ACK, &dummy, 1);
                }
                break;

            case PACKET_ACK:
                // 心跳回复，无需处理
                break;

            case PACKET_STATUS:
                // 状态包，可以用于调试
                break;

            default:
                Serial.printf("WiFiSerial: Unknown packet type: %d\n", packet.type);
                break;
        }
    }
}

// 串口任务
void WiFiSerialBridge::serialTask(void* parameter) {
    WiFiSerialBridge* bridge = (WiFiSerialBridge*)parameter;

    Serial.println("WiFiSerial: Serial task started");

    while (true) {
        // 处理接收队列中的数据，输出到串口
        SerialPacket packet;
        while (bridge->serial_rx_queue &&
               xQueueReceive(bridge->serial_rx_queue, &packet, 0) == pdTRUE) {
            if (packet.type == PACKET_SERIAL_DATA && packet.length > 0) {
                // 输出到串口
                Serial.write(packet.data, packet.length);
            }
        }

        // 读取串口数据，发送到网络
        if (Serial.available()) {
            SerialPacket tx_packet;
            tx_packet.type = PACKET_SERIAL_DATA;
            tx_packet.timestamp = millis();
            tx_packet.length = 0;

            // 读取串口数据
            while (Serial.available() && tx_packet.length < SERIAL_BUFFER_SIZE) {
                tx_packet.data[tx_packet.length++] = Serial.read();
            }

            if (tx_packet.length > 0) {
                tx_packet.checksum = bridge->calculateChecksum(tx_packet.data, tx_packet.length);

                // 发送到发送队列
                if (bridge->serial_tx_queue) {
                    xQueueSend(bridge->serial_tx_queue, &tx_packet, 0);
                }
            }
        }

        vTaskDelay(1 / portTICK_PERIOD_MS);
    }
}
